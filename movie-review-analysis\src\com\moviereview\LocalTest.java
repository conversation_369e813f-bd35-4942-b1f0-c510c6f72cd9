package com.moviereview;

import java.io.*;
import java.util.*;

/**
 * 本地测试类 - 不依赖Hadoop环境
 * 用于验证MapReduce逻辑的正确性
 */
public class LocalTest {
    
    public static void main(String[] args) {
        System.out.println("=== 电影评分分析本地测试 ===");
        
        // 创建测试数据
        String[] testData = {
            "RATING_ID,USER_MD5,MOVIE_ID,RATING,RATING_TIME",
            "1359352573,0ab7e3efacd56983f16503572d2b9915,5113101,2,2018-09-05 19:42:07",
            "1598245094,84dfd3f91dd85ea105bc74a4f0d7a067,5113101,1,2019-07-09 14:52:07",
            "311937819,c9a47fd59b55967ceac07cac6d5f270c,3718526,3,2010-11-05 22:15:44",
            "457663846,18cbf971bdf17336056674bb8fad7ea2,3718526,4,2011-11-14 22:31:02",
            "123456789,abcdef1234567890abcdef1234567890,3718526,5,2020-01-01 10:00:00",
            "987654321,1234567890abcdef1234567890abcdef,1234567,4,2021-06-15 15:30:00",
            "555666777,fedcba0987654321fedcba0987654321,1234567,3,2021-07-20 09:45:00"
        };
        
        System.out.println("测试数据：");
        for (String line : testData) {
            System.out.println(line);
        }
        System.out.println();
        
        // 模拟Mapper处理
        System.out.println("=== Mapper阶段 ===");
        Map<String, List<Double>> mapperOutput = new HashMap<>();
        
        for (int i = 1; i < testData.length; i++) { // 跳过标题行
            String line = testData[i];
            try {
                String[] fields = parseCSVLine(line);
                if (fields.length >= 4) {
                    String movieId = cleanField(fields[2]);
                    String ratingStr = cleanField(fields[3]);
                    
                    if (!movieId.isEmpty() && !ratingStr.isEmpty()) {
                        double rating = Double.parseDouble(ratingStr);
                        if (rating >= 1.0 && rating <= 5.0) {
                            mapperOutput.computeIfAbsent(movieId, k -> new ArrayList<>()).add(rating);
                            System.out.println("Mapper输出: (" + movieId + ", " + rating + ")");
                        }
                    }
                }
            } catch (Exception e) {
                System.err.println("解析错误: " + line);
            }
        }
        System.out.println();
        
        // 模拟Combiner处理
        System.out.println("=== Combiner阶段 ===");
        Map<String, Double> combinerOutput = new HashMap<>();
        
        for (Map.Entry<String, List<Double>> entry : mapperOutput.entrySet()) {
            String movieId = entry.getKey();
            List<Double> ratings = entry.getValue();
            
            double sum = 0.0;
            for (double rating : ratings) {
                sum += rating;
            }
            double average = sum / ratings.size();
            combinerOutput.put(movieId, average);
            
            System.out.println("Combiner输出: (" + movieId + ", " + String.format("%.2f", average) + 
                             ") [压缩: " + ratings.size() + " -> 1]");
        }
        System.out.println();
        
        // 模拟Partitioner处理
        System.out.println("=== Partitioner阶段 ===");
        int numPartitions = 2;
        Map<Integer, List<String>> partitions = new HashMap<>();
        
        for (String movieId : combinerOutput.keySet()) {
            int partition = getPartition(movieId, numPartitions);
            partitions.computeIfAbsent(partition, k -> new ArrayList<>()).add(movieId);
            System.out.println("电影 " + movieId + " -> 分区 " + partition);
        }
        System.out.println();
        
        // 模拟Reducer处理
        System.out.println("=== Reducer阶段 ===");
        Map<String, Double> finalResults = new HashMap<>();
        
        for (Map.Entry<Integer, List<String>> partition : partitions.entrySet()) {
            System.out.println("处理分区 " + partition.getKey() + ":");
            for (String movieId : partition.getValue()) {
                double average = combinerOutput.get(movieId);
                double roundedAverage = Math.round(average * 100.0) / 100.0;
                finalResults.put(movieId, roundedAverage);
                System.out.println("  Reducer输出: (" + movieId + ", " + String.format("%.2f", roundedAverage) + ")");
            }
        }
        System.out.println();
        
        // 显示最终结果
        System.out.println("=== 最终结果 ===");
        System.out.println("电影ID\t平均评分");
        System.out.println("----------------");
        
        // 按电影ID排序显示
        finalResults.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> System.out.println(entry.getKey() + "\t" + String.format("%.2f", entry.getValue())));
        
        // 统计信息
        System.out.println();
        System.out.println("=== 统计信息 ===");
        System.out.println("处理的电影总数: " + finalResults.size());
        System.out.println("最高评分: " + String.format("%.2f", Collections.max(finalResults.values())));
        System.out.println("最低评分: " + String.format("%.2f", Collections.min(finalResults.values())));
        
        double totalRating = finalResults.values().stream().mapToDouble(Double::doubleValue).sum();
        double overallAverage = totalRating / finalResults.size();
        System.out.println("总体平均评分: " + String.format("%.2f", overallAverage));
        
        // 评分分布
        long highRating = finalResults.values().stream().mapToLong(v -> v >= 4.0 ? 1 : 0).sum();
        long mediumRating = finalResults.values().stream().mapToLong(v -> v >= 3.0 && v < 4.0 ? 1 : 0).sum();
        long lowRating = finalResults.values().stream().mapToLong(v -> v < 3.0 ? 1 : 0).sum();
        
        System.out.println("评分分布:");
        System.out.println("  高评分(4.0+): " + highRating + " 部");
        System.out.println("  中评分(3.0-3.9): " + mediumRating + " 部");
        System.out.println("  低评分(<3.0): " + lowRating + " 部");
        
        System.out.println();
        System.out.println("=== 测试完成 ===");
        System.out.println("MapReduce逻辑验证成功！");
    }
    
    // 解析CSV行
    private static String[] parseCSVLine(String line) {
        return line.split(",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)");
    }
    
    // 清理字段
    private static String cleanField(String field) {
        if (field == null) return "";
        return field.replace("\"", "").trim();
    }
    
    // 分区函数
    private static int getPartition(String movieId, int numPartitions) {
        return (movieId.hashCode() & Integer.MAX_VALUE) % numPartitions;
    }
}
