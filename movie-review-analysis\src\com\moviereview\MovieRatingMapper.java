package com.moviereview;

import java.io.IOException;
import org.apache.hadoop.io.DoubleWritable;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

/**
 * 电影评分Mapper类
 * 
 * 功能：
 * 1. 解析输入的CSV数据
 * 2. 提取电影ID和评分信息
 * 3. 过滤无效数据
 * 4. 输出键值对：(movieId, rating)
 * 
 * 输入格式：RATING_ID,USER_MD5,MOVIE_ID,RATING,RATING_TIME
 * 输出格式：(movieId, rating)
 */
public class MovieRatingMapper extends Mapper<LongWritable, Text, Text, DoubleWritable> {
    
    // 重用对象，避免频繁创建对象
    private Text movieId = new Text();
    private DoubleWritable rating = new DoubleWritable();
    
    // 统计计数器
    private long totalLines = 0;
    private long validLines = 0;
    private long invalidLines = 0;
    
    @Override
    protected void map(LongWritable key, Text value, Context context) 
            throws IOException, InterruptedException {
        
        String line = value.toString().trim();
        totalLines++;
        
        // 跳过空行
        if (line.isEmpty()) {
            return;
        }
        
        // 跳过标题行
        if (line.startsWith("RATING_ID") || line.startsWith("COMMENT_ID") || 
            line.startsWith("\"RATING_ID\"") || line.startsWith("\"COMMENT_ID\"")) {
            return;
        }
        
        try {
            // 解析CSV数据
            String[] fields = parseCSVLine(line);
            
            // 检查字段数量
            if (fields.length >= 4) {
                // 提取电影ID和评分
                String movieIdStr = cleanField(fields[2]);
                String ratingStr = cleanField(fields[3]);
                
                // 验证数据有效性
                if (!movieIdStr.isEmpty() && !ratingStr.isEmpty()) {
                    double ratingValue = Double.parseDouble(ratingStr);
                    
                    // 过滤有效评分范围 (1-5)
                    if (ratingValue >= 1.0 && ratingValue <= 5.0) {
                        movieId.set(movieIdStr);
                        rating.set(ratingValue);
                        context.write(movieId, rating);
                        validLines++;
                    } else {
                        invalidLines++;
                        // 记录无效评分
                        context.getCounter("MovieRating", "InvalidRatingRange").increment(1);
                    }
                } else {
                    invalidLines++;
                    context.getCounter("MovieRating", "EmptyFields").increment(1);
                }
            } else {
                invalidLines++;
                context.getCounter("MovieRating", "InsufficientFields").increment(1);
            }
        } catch (NumberFormatException e) {
            invalidLines++;
            context.getCounter("MovieRating", "NumberFormatError").increment(1);
            // 可选：记录错误日志
            System.err.println("数字格式错误: " + line);
        } catch (Exception e) {
            invalidLines++;
            context.getCounter("MovieRating", "OtherErrors").increment(1);
            System.err.println("解析错误: " + line + ", 错误: " + e.getMessage());
        }
    }
    
    /**
     * 解析CSV行，处理引号包围的字段
     * @param line CSV行
     * @return 字段数组
     */
    private String[] parseCSVLine(String line) {
        // 简单的CSV解析，处理引号包围的字段
        return line.split(",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)");
    }
    
    /**
     * 清理字段，去除引号和空格
     * @param field 原始字段
     * @return 清理后的字段
     */
    private String cleanField(String field) {
        if (field == null) {
            return "";
        }
        return field.replace("\"", "").trim();
    }
    
    @Override
    protected void cleanup(Context context) throws IOException, InterruptedException {
        // 输出统计信息
        System.out.println("Mapper统计信息:");
        System.out.println("总行数: " + totalLines);
        System.out.println("有效行数: " + validLines);
        System.out.println("无效行数: " + invalidLines);
        System.out.println("有效率: " + String.format("%.2f%%", (double)validLines/totalLines*100));
        
        // 更新计数器
        context.getCounter("MovieRating", "TotalLines").increment(totalLines);
        context.getCounter("MovieRating", "ValidLines").increment(validLines);
        context.getCounter("MovieRating", "InvalidLines").increment(invalidLines);
    }
}
