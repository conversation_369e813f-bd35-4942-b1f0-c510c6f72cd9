package com.moviereview;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.DoubleWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;

/**
 * 电影评分分析 - MapReduce主驱动类
 * 
 * 功能：分析电影评分数据，计算每部电影的平均评分
 * 使用Combiner和Partitioner优化性能
 * 
 * <AUTHOR>
 */
public class MovieReviewAnalysis {
    
    public static void main(String[] args) throws Exception {
        // 检查命令行参数
        if (args.length != 2) {
            System.err.println("用法: MovieReviewAnalysis <输入路径> <输出路径>");
            System.err.println("示例: hadoop jar movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews");
            System.exit(-1);
        }
        
        // 创建配置对象
        Configuration conf = new Configuration();
        
        // 设置一些优化参数
        conf.set("mapreduce.job.reduces", "2"); // 设置Reducer数量
        conf.set("mapreduce.map.memory.mb", "1024"); // 设置Map任务内存
        conf.set("mapreduce.reduce.memory.mb", "2048"); // 设置Reduce任务内存
        
        // 创建作业实例
        Job job = Job.getInstance(conf, "电影评分分析");
        
        // 设置主类
        job.setJarByClass(MovieReviewAnalysis.class);
        
        // 设置Mapper类
        job.setMapperClass(MovieRatingMapper.class);
        
        // 设置Combiner类（用于本地聚合优化）
        job.setCombinerClass(MovieRatingCombiner.class);
        
        // 设置Partitioner类（用于数据分发优化）
        job.setPartitionerClass(MovieRatingPartitioner.class);
        
        // 设置Reducer类
        job.setReducerClass(MovieRatingReducer.class);
        
        // 设置输出键值类型
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(DoubleWritable.class);
        
        // 设置Mapper输出键值类型
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(DoubleWritable.class);
        
        // 设置输入输出路径
        FileInputFormat.addInputPath(job, new Path(args[0]));
        FileOutputFormat.setOutputPath(job, new Path(args[1]));
        
        // 打印作业信息
        System.out.println("=== 电影评分分析作业开始 ===");
        System.out.println("输入路径: " + args[0]);
        System.out.println("输出路径: " + args[1]);
        System.out.println("使用Combiner优化: 是");
        System.out.println("使用Partitioner优化: 是");
        System.out.println("========================");
        
        // 等待作业完成并返回结果
        boolean success = job.waitForCompletion(true);
        
        if (success) {
            System.out.println("=== 作业执行成功 ===");
            System.out.println("请查看输出目录获取结果: " + args[1]);
        } else {
            System.err.println("=== 作业执行失败 ===");
        }
        
        System.exit(success ? 0 : 1);
    }
}
