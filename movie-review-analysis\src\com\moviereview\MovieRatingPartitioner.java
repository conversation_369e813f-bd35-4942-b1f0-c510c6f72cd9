package com.moviereview;

import org.apache.hadoop.io.DoubleWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Partitioner;

/**
 * 电影评分Partitioner类
 * 
 * 功能：
 * 1. 控制数据分发到不同Reducer的策略
 * 2. 确保相同电影ID的数据发送到同一个Reducer
 * 3. 实现负载均衡，避免数据倾斜
 * 4. 提高并行处理效率
 * 
 * 分区策略：
 * - 使用电影ID的哈希值进行分区
 * - 确保数据均匀分布到各个Reducer
 * - 避免某个Reducer处理过多数据
 * 
 * 优化效果：
 * - 防止数据倾斜问题
 * - 提高Reducer并行度
 * - 优化资源利用率
 */
public class MovieRatingPartitioner extends Partitioner<Text, DoubleWritable> {
    
    /**
     * 获取分区号
     * 
     * @param key 电影ID
     * @param value 评分值
     * @param numPartitions Reducer数量
     * @return 分区号 (0 到 numPartitions-1)
     */
    @Override
    public int getPartition(Text key, DoubleWritable value, int numPartitions) {
        // 使用电影ID的哈希值进行分区
        // 确保相同电影ID的数据发送到同一个Reducer
        
        // 获取电影ID的哈希值
        int hashCode = key.hashCode();
        
        // 确保哈希值为正数
        int positiveHash = hashCode & Integer.MAX_VALUE;
        
        // 计算分区号
        int partition = positiveHash % numPartitions;
        
        return partition;
    }
    
    /**
     * 自定义分区策略（可选）
     * 根据电影ID的特征进行更智能的分区
     */
    public int getCustomPartition(Text key, DoubleWritable value, int numPartitions) {
        String movieId = key.toString();
        
        try {
            // 如果电影ID是数字，可以根据数字范围分区
            long movieIdNum = Long.parseLong(movieId);
            
            // 根据电影ID范围分区
            if (movieIdNum < 1000000) {
                return 0 % numPartitions;
            } else if (movieIdNum < 10000000) {
                return 1 % numPartitions;
            } else {
                return 2 % numPartitions;
            }
        } catch (NumberFormatException e) {
            // 如果不是数字，使用默认哈希分区
            return (key.hashCode() & Integer.MAX_VALUE) % numPartitions;
        }
    }
}
