# 电影评分分析项目 - 快速操作参考

## 📁 项目结构

```
/root/movie-review-analysis/
├── src/com/moviereview/           # Java源码
│   ├── MovieReviewAnalysis.java   # 主驱动类
│   ├── MovieRatingMapper.java     # Mapper类
│   ├── MovieRatingCombiner.java   # Combiner类
│   ├── MovieRatingPartitioner.java # Partitioner类
│   └── MovieRatingReducer.java    # Reducer类
├── build/                         # 编译输出
│   └── movie-review-analysis.jar  # JAR包
├── ratings.csv                    # 原始数据
├── clean_ratings.csv              # 清理后数据
└── movie_analysis_result.txt      # 最终结果
```

## ⚡ 快速执行命令

### 1. 启动Hadoop
```bash
cd /opt/hadoop/current
start-dfs.sh && start-yarn.sh
jps  # 验证服务
```

### 2. 编译项目
```bash
cd /root/movie-review-analysis
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*
rm -rf build/* && mkdir -p build
javac -cp $HADOOP_CLASSPATH -d build src/com/moviereview/*.java
cd build && jar cf movie-review-analysis.jar com/moviereview/*.class && cd ..
```

### 3. 准备数据
```bash
tail -n +2 ratings.csv > clean_ratings.csv
hdfs dfs -mkdir -p /user/input/moviereviews
hdfs dfs -rm -f /user/input/moviereviews/clean_ratings.csv
hdfs dfs -put clean_ratings.csv /user/input/moviereviews/
```

### 4. 运行作业
```bash
hdfs dfs -rm -r /user/output/moviereviews
hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews
```

### 5. 查看结果
```bash
hdfs dfs -getmerge /user/output/moviereviews/part-r-* movie_analysis_result.txt
head -10 movie_analysis_result.txt
wc -l movie_analysis_result.txt
sort -k2 -nr movie_analysis_result.txt | head -10
```

### 6. 停止服务
```bash
stop-yarn.sh && stop-dfs.sh
```

## 🔍 常用检查命令

### 服务状态
```bash
jps                                    # 查看Java进程
hdfs dfsadmin -report                  # HDFS状态
yarn node -list                       # YARN节点
```

### 文件检查
```bash
ls -la src/com/moviereview/            # 源码文件
ls -la build/                          # 编译结果
hdfs dfs -ls /user/input/moviereviews  # HDFS输入
hdfs dfs -ls /user/output/moviereviews # HDFS输出
```

### 数据验证
```bash
head -5 ratings.csv                    # 原始数据样本
head -5 clean_ratings.csv              # 清理后数据
hdfs dfs -cat /user/input/moviereviews/clean_ratings.csv | head -3  # HDFS数据
```

## 🌐 Web界面

- **NameNode**: http://虚拟机IP:9870
- **ResourceManager**: http://虚拟机IP:8088

## 🚨 故障排除

### 编译失败
```bash
java -version && javac -version        # 检查Java
echo $HADOOP_CLASSPATH                 # 检查类路径
source ~/.bashrc                       # 重新加载环境
```

### 服务启动失败
```bash
stop-dfs.sh && stop-yarn.sh           # 停止服务
start-dfs.sh && start-yarn.sh         # 重新启动
tail -f $HADOOP_HOME/logs/*.log       # 查看日志
```

### HDFS操作失败
```bash
hdfs dfsadmin -safemode leave          # 退出安全模式
hdfs fsck /                            # 检查文件系统
```

### 作业失败
```bash
yarn application -list                 # 查看应用列表
yarn logs -applicationId <app_id>      # 查看应用日志
```

## 📊 结果分析命令

```bash
# 基本统计
wc -l movie_analysis_result.txt

# 评分最高的电影
sort -k2 -nr movie_analysis_result.txt | head -10

# 评分最低的电影
sort -k2 -n movie_analysis_result.txt | head -10

# 评分分布
awk '{if($2>=4.0) h++; else if($2>=3.0) m++; else l++} END {print "高评分:"h" 中评分:"m" 低评分:"l}' movie_analysis_result.txt

# 平均评分
awk '{sum+=$2; count++} END {print "总体平均评分:", sum/count}' movie_analysis_result.txt
```

## 📝 一键执行脚本

创建 `run_all.sh` 文件：

```bash
#!/bin/bash
echo "=== 启动Hadoop ==="
cd /opt/hadoop/current
start-dfs.sh && start-yarn.sh

echo "=== 编译项目 ==="
cd /root/movie-review-analysis
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*
rm -rf build/* && mkdir -p build
javac -cp $HADOOP_CLASSPATH -d build src/com/moviereview/*.java
cd build && jar cf movie-review-analysis.jar com/moviereview/*.class && cd ..

echo "=== 准备数据 ==="
tail -n +2 ratings.csv > clean_ratings.csv
hdfs dfs -mkdir -p /user/input/moviereviews
hdfs dfs -rm -f /user/input/moviereviews/clean_ratings.csv
hdfs dfs -put clean_ratings.csv /user/input/moviereviews/

echo "=== 运行作业 ==="
hdfs dfs -rm -r /user/output/moviereviews
hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews

echo "=== 查看结果 ==="
hdfs dfs -getmerge /user/output/moviereviews/part-r-* movie_analysis_result.txt
echo "处理的电影总数: $(wc -l < movie_analysis_result.txt)"
echo "评分最高的5部电影:"
sort -k2 -nr movie_analysis_result.txt | head -5

echo "=== 完成 ==="
```

使用方法：
```bash
chmod +x run_all.sh
./run_all.sh
```
