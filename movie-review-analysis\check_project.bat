@echo off
REM 项目完整性检查脚本
REM 功能：验证项目文件是否完整

echo === 电影评分分析项目完整性检查 ===
echo.

set ERROR_COUNT=0

echo 检查项目结构...
echo ==================

REM 检查源代码文件
echo [1] 检查Java源代码文件：
if exist "src\com\moviereview\MovieReviewAnalysis.java" (
    echo ✓ MovieReviewAnalysis.java - 主驱动类
) else (
    echo ✗ MovieReviewAnalysis.java - 缺失
    set /a ERROR_COUNT+=1
)

if exist "src\com\moviereview\MovieRatingMapper.java" (
    echo ✓ MovieRatingMapper.java - Mapper类
) else (
    echo ✗ MovieRatingMapper.java - 缺失
    set /a ERROR_COUNT+=1
)

if exist "src\com\moviereview\MovieRatingCombiner.java" (
    echo ✓ MovieRatingCombiner.java - Combiner类
) else (
    echo ✗ MovieRatingCombiner.java - 缺失
    set /a ERROR_COUNT+=1
)

if exist "src\com\moviereview\MovieRatingPartitioner.java" (
    echo ✓ MovieRatingPartitioner.java - Partitioner类
) else (
    echo ✗ MovieRatingPartitioner.java - 缺失
    set /a ERROR_COUNT+=1
)

if exist "src\com\moviereview\MovieRatingReducer.java" (
    echo ✓ MovieRatingReducer.java - Reducer类
) else (
    echo ✗ MovieRatingReducer.java - 缺失
    set /a ERROR_COUNT+=1
)

echo.
echo [2] 检查脚本文件：
if exist "compile.bat" (
    echo ✓ compile.bat - Windows编译脚本
) else (
    echo ✗ compile.bat - 缺失
    set /a ERROR_COUNT+=1
)

if exist "compile.sh" (
    echo ✓ compile.sh - Linux编译脚本
) else (
    echo ✗ compile.sh - 缺失
    set /a ERROR_COUNT+=1
)

if exist "prepare_data.bat" (
    echo ✓ prepare_data.bat - 数据预处理脚本
) else (
    echo ✗ prepare_data.bat - 缺失
    set /a ERROR_COUNT+=1
)

if exist "run_job.bat" (
    echo ✓ run_job.bat - 作业运行脚本
) else (
    echo ✗ run_job.bat - 缺失
    set /a ERROR_COUNT+=1
)

echo.
echo [3] 检查目录结构：
if exist "src" (
    echo ✓ src - 源代码目录
) else (
    echo ✗ src - 缺失
    set /a ERROR_COUNT+=1
)

if exist "build" (
    echo ✓ build - 构建目录
) else (
    echo ✗ build - 缺失
    set /a ERROR_COUNT+=1
)

if exist "data" (
    echo ✓ data - 数据目录
) else (
    echo ✗ data - 缺失
    set /a ERROR_COUNT+=1
)

if exist "lib" (
    echo ✓ lib - 库目录
) else (
    echo ✗ lib - 缺失
    set /a ERROR_COUNT+=1
)

echo.
echo [4] 检查文档文件：
if exist "README.md" (
    echo ✓ README.md - 项目说明文档
) else (
    echo ✗ README.md - 缺失
    set /a ERROR_COUNT+=1
)

if exist "..\操作指南.md" (
    echo ✓ 操作指南.md - 详细教程文档
) else (
    echo ✗ 操作指南.md - 缺失
    set /a ERROR_COUNT+=1
)

echo.
echo [5] 检查数据文件：
if exist "..\ratings.csv" (
    echo ✓ ratings.csv - 评分数据文件
    for %%A in ("..\ratings.csv") do echo   文件大小: %%~zA 字节
) else (
    echo ✗ ratings.csv - 缺失
    set /a ERROR_COUNT+=1
)

if exist "..\comments.csv" (
    echo ✓ comments.csv - 评论数据文件
    for %%A in ("..\comments.csv") do echo   文件大小: %%~zA 字节
) else (
    echo ! comments.csv - 可选文件，未找到
)

echo.
echo ==================
echo 检查结果汇总
echo ==================

if %ERROR_COUNT% equ 0 (
    echo ✅ 项目检查通过！所有必需文件都存在。
    echo.
    echo 📊 项目统计：
    for /f %%i in ('dir /s /b *.java ^| find /c /v ""') do echo Java源文件数量: %%i
    for /f %%i in ('dir /b *.bat ^| find /c /v ""') do echo 脚本文件数量: %%i
    echo.
    echo 🚀 下一步操作：
    echo 1. 运行 demo.bat 查看项目演示
    echo 2. 配置Hadoop环境
    echo 3. 运行 compile.bat 编译项目
    echo 4. 运行 prepare_data.bat 准备数据
    echo 5. 运行 run_job.bat 执行作业
) else (
    echo ❌ 项目检查失败！发现 %ERROR_COUNT% 个问题。
    echo.
    echo 🔧 解决方案：
    echo 1. 检查文件是否正确创建
    echo 2. 确保在正确的目录中
    echo 3. 重新按照操作指南创建缺失文件
)

echo.
echo ==================
echo 检查完成
echo ==================

pause
