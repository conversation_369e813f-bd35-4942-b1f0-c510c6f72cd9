# 电影评分分析项目 - CentOS虚拟机Hadoop集群部署教程

## 📋 教程概述

本教程提供了在CentOS虚拟机上部署和运行电影评分分析MapReduce项目的标准操作步骤。

## 🎯 环境信息

- **操作系统**：CentOS 7/8
- **项目路径**：`/root/movie-review-analysis`
- **数据文件路径**：`/root/movie-review-analysis/ratings.csv`
- **Hadoop安装路径**：`/opt/hadoop`
- **Java安装路径**：`/usr/lib/jvm/java-1.8.0-openjdk`
- **用户**：root

## 🚀 第一阶段：系统环境准备

### 步骤1：系统更新和基础软件安装

**当前路径**：`/root`

```bash
# 更新系统
yum update -y

# 安装必要的工具
yum install -y wget curl vim net-tools which

# 安装Java 8
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 验证Java安装
java -version
javac -version
```

**预期输出**：
```
openjdk version "1.8.0_XXX"
OpenJDK Runtime Environment (build 1.8.0_XXX)
OpenJDK 64-Bit Server VM (build XXX)
```

### 步骤2：配置Java环境变量

**当前路径**：`/root`

```bash
# 查找Java安装路径
find /usr/lib/jvm -name "java-1.8.0-openjdk*" -type d

# 设置Java环境变量
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
echo 'export PATH=$PATH:$JAVA_HOME/bin' >> ~/.bashrc

# 重新加载环境变量
source ~/.bashrc

# 验证环境变量
echo $JAVA_HOME
which java
which javac
```

### 步骤3：下载和安装Hadoop

**当前路径**：`/root`

```bash
# 创建Hadoop安装目录
mkdir -p /opt/hadoop
cd /opt/hadoop

# 下载Hadoop 3.3.4
wget https://archive.apache.org/dist/hadoop/common/hadoop-3.3.4/hadoop-3.3.4.tar.gz

# 如果下载失败，可以使用镜像站点
# wget https://mirrors.tuna.tsinghua.edu.cn/apache/hadoop/common/hadoop-3.3.4/hadoop-3.3.4.tar.gz

# 解压Hadoop
tar -xzf hadoop-3.3.4.tar.gz

# 创建软链接
ln -s hadoop-3.3.4 current

# 设置Hadoop环境变量
echo 'export HADOOP_HOME=/opt/hadoop/current' >> ~/.bashrc
echo 'export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop' >> ~/.bashrc
echo 'export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin' >> ~/.bashrc

# 重新加载环境变量
source ~/.bashrc

# 验证Hadoop安装
hadoop version
```

### 步骤4：配置SSH免密登录

**当前路径**：`/root`

```bash
# 安装SSH服务
yum install -y openssh-server openssh-clients

# 启动SSH服务
systemctl start sshd
systemctl enable sshd

# 生成SSH密钥
ssh-keygen -t rsa -P '' -f ~/.ssh/id_rsa

# 配置免密登录
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
chmod 0600 ~/.ssh/authorized_keys

# 测试SSH连接
ssh localhost exit
```

## 🔧 第二阶段：Hadoop配置

### 步骤5：配置Hadoop核心文件

**当前路径**：`/opt/hadoop/current/etc/hadoop`

```bash
cd /opt/hadoop/current/etc/hadoop
```

#### 5.1 配置 hadoop-env.sh

**文件路径**：`/opt/hadoop/current/etc/hadoop/hadoop-env.sh`

```bash
# 编辑hadoop-env.sh
vim hadoop-env.sh

# 添加以下内容（在文件末尾）
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export HADOOP_HOME=/opt/hadoop/current
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
export HADOOP_LOG_DIR=$HADOOP_HOME/logs
```

#### 5.2 配置 core-site.xml

**文件路径**：`/opt/hadoop/current/etc/hadoop/core-site.xml`

```bash
# 备份原文件
cp core-site.xml core-site.xml.bak

# 编辑core-site.xml
cat > core-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>fs.defaultFS</name>
        <value>hdfs://localhost:9000</value>
        <description>NameNode URI</description>
    </property>
    <property>
        <name>hadoop.tmp.dir</name>
        <value>/opt/hadoop/data/tmp</value>
        <description>Temporary directory</description>
    </property>
    <property>
        <name>hadoop.http.staticuser.user</name>
        <value>root</value>
    </property>
</configuration>
EOF
```

#### 5.3 配置 hdfs-site.xml

**文件路径**：`/opt/hadoop/current/etc/hadoop/hdfs-site.xml`

```bash
# 编辑hdfs-site.xml
cat > hdfs-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>dfs.replication</name>
        <value>1</value>
        <description>Default block replication</description>
    </property>
    <property>
        <name>dfs.namenode.name.dir</name>
        <value>/opt/hadoop/data/namenode</value>
        <description>NameNode directory</description>
    </property>
    <property>
        <name>dfs.datanode.data.dir</name>
        <value>/opt/hadoop/data/datanode</value>
        <description>DataNode directory</description>
    </property>
    <property>
        <name>dfs.namenode.http-address</name>
        <value>0.0.0.0:9870</value>
        <description>NameNode web interface</description>
    </property>
    <property>
        <name>dfs.permissions.enabled</name>
        <value>false</value>
        <description>Disable permissions checking</description>
    </property>
</configuration>
EOF
```

#### 5.4 配置 mapred-site.xml

**文件路径**：`/opt/hadoop/current/etc/hadoop/mapred-site.xml`

```bash
# 编辑mapred-site.xml
cat > mapred-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>mapreduce.framework.name</name>
        <value>yarn</value>
        <description>MapReduce framework</description>
    </property>
    <property>
        <name>mapreduce.application.classpath</name>
        <value>$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*</value>
    </property>
    <property>
        <name>yarn.app.mapreduce.am.env</name>
        <value>HADOOP_MAPRED_HOME=$HADOOP_HOME</value>
    </property>
    <property>
        <name>mapreduce.map.env</name>
        <value>HADOOP_MAPRED_HOME=$HADOOP_HOME</value>
    </property>
    <property>
        <name>mapreduce.reduce.env</name>
        <value>HADOOP_MAPRED_HOME=$HADOOP_HOME</value>
    </property>
</configuration>
EOF
```

#### 5.5 配置 yarn-site.xml

**文件路径**：`/opt/hadoop/current/etc/hadoop/yarn-site.xml`

```bash
# 编辑yarn-site.xml
cat > yarn-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
        <description>Auxiliary services</description>
    </property>
    <property>
        <name>yarn.nodemanager.aux-services.mapreduce.shuffle.class</name>
        <value>org.apache.hadoop.mapred.ShuffleHandler</value>
    </property>
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>localhost</value>
    </property>
    <property>
        <name>yarn.resourcemanager.webapp.address</name>
        <value>0.0.0.0:8088</value>
    </property>
    <property>
        <name>yarn.nodemanager.vmem-check-enabled</name>
        <value>false</value>
        <description>Disable virtual memory checking</description>
    </property>
</configuration>
EOF
```

### 步骤6：创建Hadoop数据目录

**当前路径**：`/opt/hadoop`

```bash
# 创建数据目录
mkdir -p /opt/hadoop/data/tmp
mkdir -p /opt/hadoop/data/namenode
mkdir -p /opt/hadoop/data/datanode
mkdir -p /opt/hadoop/logs

# 设置目录权限
chmod 755 /opt/hadoop/data
chmod 755 /opt/hadoop/data/*

# 验证目录结构
ls -la /opt/hadoop/data/
```

### 步骤7：格式化NameNode

**当前路径**：`/opt/hadoop/current`

```bash
# 格式化NameNode（仅第一次需要）
hdfs namenode -format

# 等待格式化完成，看到以下信息表示成功：
# "Storage directory /opt/hadoop/data/namenode has been successfully formatted."
```

## 🚀 第三阶段：启动Hadoop集群

### 步骤8：启动Hadoop服务

**当前路径**：`/opt/hadoop/current`

```bash
# 启动HDFS服务
start-dfs.sh

# 等待启动完成（约30-60秒）
sleep 30

# 启动YARN服务
start-yarn.sh

# 等待启动完成（约30-60秒）
sleep 30
```

### 步骤9：验证Hadoop服务状态

**当前路径**：`/opt/hadoop/current`

```bash
# 检查Java进程
jps

# 预期看到以下进程：
# XXXX NameNode
# XXXX DataNode
# XXXX ResourceManager
# XXXX NodeManager
# XXXX SecondaryNameNode

# 检查HDFS状态
hdfs dfsadmin -report

# 测试HDFS基本操作
hdfs dfs -mkdir -p /user/root
hdfs dfs -ls /
```

### 步骤10：配置防火墙（如果启用）

**当前路径**：`/root`

```bash
# 检查防火墙状态
systemctl status firewalld

# 如果防火墙启用，开放必要端口
firewall-cmd --permanent --add-port=9000/tcp    # NameNode
firewall-cmd --permanent --add-port=9870/tcp    # NameNode Web UI
firewall-cmd --permanent --add-port=8088/tcp    # ResourceManager Web UI
firewall-cmd --permanent --add-port=9864/tcp    # DataNode Web UI
firewall-cmd --reload

# 或者临时关闭防火墙（仅用于测试）
systemctl stop firewalld
```

## 📁 第四阶段：项目部署

### 步骤11：创建项目目录和传输文件

**当前路径**：`/root`

```bash
# 创建项目目录
mkdir -p /root/movie-review-analysis
cd /root/movie-review-analysis

# 创建项目结构
mkdir -p src/com/moviereview
mkdir -p build
mkdir -p data
mkdir -p lib

# 显示项目结构
tree /root/movie-review-analysis
# 或者使用ls
find /root/movie-review-analysis -type d
```

**注意**：您需要将以下文件从Windows系统传输到CentOS虚拟机：

1. **Java源码文件**（传输到 `/root/movie-review-analysis/src/com/moviereview/`）：
   - MovieReviewAnalysis.java
   - MovieRatingMapper.java
   - MovieRatingCombiner.java
   - MovieRatingPartitioner.java
   - MovieRatingReducer.java

2. **数据文件**（传输到 `/root/movie-review-analysis/data/`）：
   - ratings.csv

**文件传输方法**：

#### 方法1：使用SCP命令（从Windows PowerShell执行）
```powershell
# 从Windows传输Java文件到虚拟机
scp D:\ldlhadoopxhd\movie-review-analysis\src\com\moviereview\*.java root@虚拟机IP:/root/movie-review-analysis/src/com/moviereview/

# 传输数据文件
scp D:\ldlhadoopxhd\ratings.csv root@虚拟机IP:/root/movie-review-analysis/data/
```

#### 方法2：使用WinSCP图形界面工具
1. 下载安装WinSCP
2. 连接到虚拟机
3. 拖拽文件到对应目录

#### 方法3：使用共享文件夹（VMware/VirtualBox）
1. 配置虚拟机共享文件夹
2. 在虚拟机中挂载共享目录
3. 复制文件到项目目录

### 步骤12：验证文件传输

**当前路径**：`/root/movie-review-analysis`

```bash
# 检查Java源码文件
ls -la src/com/moviereview/
echo "应该看到5个.java文件"

# 检查数据文件
ls -la data/ratings.csv
echo "应该看到ratings.csv文件"

# 查看文件内容样本
head -5 data/ratings.csv
```

## 🔨 第五阶段：编译和运行

### 步骤13：编译MapReduce项目

**当前路径**：`/root/movie-review-analysis`

```bash
# 设置Hadoop类路径
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*

# 编译Java源码
javac -cp $HADOOP_CLASSPATH -d build src/com/moviereview/*.java

# 检查编译结果
ls -la build/com/moviereview/

# 创建JAR包
cd build
jar cf movie-review-analysis.jar com/moviereview/*.class

# 验证JAR包
jar tf movie-review-analysis.jar

# 返回项目根目录
cd /root/movie-review-analysis
```

### 步骤14：准备输入数据

**当前路径**：`/root/movie-review-analysis`

```bash
# 处理原始数据（去除标题行）
tail -n +2 data/ratings.csv > data/clean_ratings.csv

# 查看处理后的数据
head -5 data/clean_ratings.csv

# 检查数据文件大小
ls -lh data/clean_ratings.csv
wc -l data/clean_ratings.csv
```

### 步骤15：上传数据到HDFS

**当前路径**：`/root/movie-review-analysis`

```bash
# 创建HDFS输入目录
hdfs dfs -mkdir -p /user/input/moviereviews

# 上传数据文件到HDFS
hdfs dfs -put data/clean_ratings.csv /user/input/moviereviews/

# 验证上传成功
hdfs dfs -ls /user/input/moviereviews

# 查看HDFS中的数据样本
hdfs dfs -cat /user/input/moviereviews/clean_ratings.csv | head -5
```

### 步骤16：运行MapReduce作业

**当前路径**：`/root/movie-review-analysis`

```bash
# 确保输出目录不存在
hdfs dfs -rm -r /user/output/moviereviews

# 提交MapReduce作业
hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews

# 作业运行过程中会显示进度信息
# 等待作业完成
```

### 步骤17：查看和分析结果

**当前路径**：`/root/movie-review-analysis`

```bash
# 查看输出目录结构
hdfs dfs -ls /user/output/moviereviews

# 查看结果内容
hdfs dfs -cat /user/output/moviereviews/part-r-* | head -20

# 下载结果到本地
hdfs dfs -getmerge /user/output/moviereviews/part-r-* movie_analysis_result.txt

# 分析结果
echo "处理的电影总数："
wc -l movie_analysis_result.txt

echo "评分最高的10部电影："
sort -k2 -nr movie_analysis_result.txt | head -10

echo "评分最低的10部电影："
sort -k2 -n movie_analysis_result.txt | head -10
```

## 🔧 第六阶段：自动化脚本

### 环境检查脚本 - check_environment.sh

**文件路径**：`/root/movie-review-analysis/check_environment.sh`

```bash
#!/bin/bash
# CentOS Hadoop环境检查脚本

echo "========================================"
echo "   CentOS Hadoop环境完整性检查"
echo "========================================"
echo

ERROR_COUNT=0
HADOOP_HOME="/opt/hadoop/current"
JAVA_HOME="/usr/lib/jvm/java-1.8.0-openjdk"

echo "[1] Java环境检查..."
echo "=================="

echo "检查Java命令..."
if command -v java &> /dev/null; then
    echo "✓ Java命令可用"
    java -version
else
    echo "✗ Java命令不可用"
    echo "请安装Java: yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel"
    ((ERROR_COUNT++))
fi

echo
echo "检查Java编译器..."
if command -v javac &> /dev/null; then
    echo "✓ javac命令可用"
    javac -version
else
    echo "✗ javac命令不可用"
    echo "请安装JDK: yum install -y java-1.8.0-openjdk-devel"
    ((ERROR_COUNT++))
fi

echo
echo "检查JAVA_HOME环境变量..."
if [ -n "$JAVA_HOME" ] && [ -d "$JAVA_HOME" ]; then
    echo "✓ JAVA_HOME已设置: $JAVA_HOME"
else
    echo "! JAVA_HOME未正确设置"
    echo "请添加到~/.bashrc: export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk"
fi

echo
echo "[2] Hadoop安装检查..."
echo "===================="

echo "检查Hadoop安装目录..."
if [ -d "$HADOOP_HOME" ]; then
    echo "✓ Hadoop安装目录存在: $HADOOP_HOME"
else
    echo "✗ Hadoop安装目录不存在: $HADOOP_HOME"
    echo "请安装Hadoop到指定目录"
    ((ERROR_COUNT++))
fi

echo
echo "检查Hadoop命令..."
if command -v hadoop &> /dev/null; then
    echo "✓ hadoop命令可用"
    hadoop version | head -1
else
    echo "✗ hadoop命令不可用"
    echo "请检查PATH环境变量"
    ((ERROR_COUNT++))
fi

if command -v hdfs &> /dev/null; then
    echo "✓ hdfs命令可用"
else
    echo "✗ hdfs命令不可用"
    ((ERROR_COUNT++))
fi

echo
echo "检查Hadoop配置文件..."
CONFIG_DIR="$HADOOP_HOME/etc/hadoop"
for config in core-site.xml hdfs-site.xml mapred-site.xml yarn-site.xml; do
    if [ -f "$CONFIG_DIR/$config" ]; then
        echo "✓ $config存在"
    else
        echo "✗ $config不存在"
        ((ERROR_COUNT++))
    fi
done

echo
echo "[3] SSH配置检查..."
echo "=================="

echo "检查SSH服务..."
if systemctl is-active --quiet sshd; then
    echo "✓ SSH服务正在运行"
else
    echo "! SSH服务未运行"
    echo "请启动SSH: systemctl start sshd"
fi

echo "检查SSH免密登录..."
if [ -f ~/.ssh/id_rsa.pub ] && [ -f ~/.ssh/authorized_keys ]; then
    echo "✓ SSH密钥已配置"
    if ssh -o BatchMode=yes -o ConnectTimeout=5 localhost exit 2>/dev/null; then
        echo "✓ SSH免密登录正常"
    else
        echo "! SSH免密登录失败"
        echo "请配置: cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys"
    fi
else
    echo "! SSH密钥未配置"
    echo "请生成密钥: ssh-keygen -t rsa -P '' -f ~/.ssh/id_rsa"
fi

echo
echo "[4] 项目文件检查..."
echo "=================="

PROJECT_DIR="/root/movie-review-analysis"
echo "检查项目目录..."
if [ -d "$PROJECT_DIR" ]; then
    echo "✓ 项目目录存在: $PROJECT_DIR"
else
    echo "! 项目目录不存在，将创建..."
    mkdir -p "$PROJECT_DIR"/{src/com/moviereview,build,data,lib}
    echo "✓ 项目目录已创建"
fi

echo "检查Java源码文件..."
JAVA_FILES=("MovieReviewAnalysis.java" "MovieRatingMapper.java" "MovieRatingCombiner.java" "MovieRatingPartitioner.java" "MovieRatingReducer.java")
for file in "${JAVA_FILES[@]}"; do
    if [ -f "$PROJECT_DIR/src/com/moviereview/$file" ]; then
        echo "✓ $file存在"
    else
        echo "✗ $file不存在"
        echo "请传输文件到: $PROJECT_DIR/src/com/moviereview/"
        ((ERROR_COUNT++))
    fi
done

echo "检查数据文件..."
if [ -f "$PROJECT_DIR/data/ratings.csv" ]; then
    echo "✓ ratings.csv存在"
    echo "文件大小: $(ls -lh $PROJECT_DIR/data/ratings.csv | awk '{print $5}')"
else
    echo "✗ ratings.csv不存在"
    echo "请传输数据文件到: $PROJECT_DIR/data/ratings.csv"
    ((ERROR_COUNT++))
fi

echo
echo "[5] 网络端口检查..."
echo "=================="

echo "检查关键端口..."
PORTS=(9000 9870 8088 9864)
for port in "${PORTS[@]}"; do
    if netstat -tuln | grep ":$port " > /dev/null; then
        echo "! 端口$port已被占用"
    else
        echo "✓ 端口$port可用"
    fi
done

echo
echo "========================================"
echo "环境检查结果"
echo "========================================"

if [ $ERROR_COUNT -eq 0 ]; then
    echo "✅ 环境检查通过！所有必需组件都已就绪。"
    echo
    echo "🚀 可以运行以下命令开始部署："
    echo "   ./deploy_complete.sh"
    echo
    echo "📋 部署前建议："
    echo "1. 确保虚拟机有足够内存（至少2GB）"
    echo "2. 确保磁盘空间充足（至少5GB）"
    echo "3. 检查网络连接正常"
else
    echo "❌ 环境检查失败！发现 $ERROR_COUNT 个问题。"
    echo
    echo "🔧 请解决以上问题后重新运行检查："
    echo "   ./check_environment.sh"
    echo
    echo "📖 详细解决方案请参考："
    echo "   CentOS虚拟机部署完整教程.md"
fi

echo
echo "========================================"
echo "检查完成"
echo "========================================"
```

### 一键部署脚本 - deploy_complete.sh

**文件路径**：`/root/movie-review-analysis/deploy_complete.sh`

```bash
#!/bin/bash
# CentOS Hadoop集群完整部署脚本

echo "========================================"
echo "  电影评分分析 - CentOS Hadoop集群部署"
echo "========================================"
echo

PROJECT_DIR="/root/movie-review-analysis"
HADOOP_HOME="/opt/hadoop/current"
JAVA_HOME="/usr/lib/jvm/java-1.8.0-openjdk"

echo "项目目录: $PROJECT_DIR"
echo "Hadoop目录: $HADOOP_HOME"
echo "Java目录: $JAVA_HOME"
echo

echo "[1/8] 环境检查..."
echo "=================="

echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "✗ Java环境未配置"
    echo "请先安装Java: yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel"
    exit 1
else
    echo "✓ Java环境正常"
    java -version
fi

echo
echo "检查Hadoop安装..."
if [ ! -d "$HADOOP_HOME" ]; then
    echo "✗ Hadoop未安装到指定目录"
    echo "请先安装Hadoop到: $HADOOP_HOME"
    exit 1
else
    echo "✓ Hadoop安装目录存在"
fi

echo
echo "检查项目文件..."
if [ ! -f "$PROJECT_DIR/src/com/moviereview/MovieReviewAnalysis.java" ]; then
    echo "✗ 项目源码文件不存在"
    echo "请确保Java源码文件已传输到: $PROJECT_DIR/src/com/moviereview/"
    exit 1
else
    echo "✓ 项目源码文件存在"
fi

echo
echo "[2/8] 启动Hadoop服务..."
echo "======================"

echo "检查Hadoop服务状态..."
if ! jps | grep -q "NameNode\|DataNode"; then
    echo "启动HDFS服务..."
    start-dfs.sh
    echo "等待HDFS服务启动（30秒）..."
    sleep 30

    echo "启动YARN服务..."
    start-yarn.sh
    echo "等待YARN服务启动（30秒）..."
    sleep 30
else
    echo "✓ Hadoop服务已在运行"
fi

echo
echo "[3/8] 验证Hadoop服务..."
echo "======================"

echo "检查Java进程..."
jps

echo
echo "验证关键服务..."
if ! jps | grep -q "NameNode"; then
    echo "✗ NameNode未启动"
    echo "请检查Hadoop配置和日志"
    exit 1
else
    echo "✓ NameNode已启动"
fi

if ! jps | grep -q "DataNode"; then
    echo "✗ DataNode未启动"
    echo "请检查Hadoop配置和日志"
    exit 1
else
    echo "✓ DataNode已启动"
fi

echo
echo "测试HDFS基本操作..."
if hdfs dfs -mkdir -p /test 2>/dev/null && hdfs dfs -rmdir /test 2>/dev/null; then
    echo "✓ HDFS操作正常"
else
    echo "✗ HDFS操作失败"
    exit 1
fi

echo
echo "[4/8] 编译MapReduce项目..."
echo "========================="

cd "$PROJECT_DIR"

echo "设置Hadoop类路径..."
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*

echo "清理构建目录..."
rm -rf build/*
mkdir -p build

echo "编译Java源码..."
if ! javac -cp "$HADOOP_CLASSPATH" -d build src/com/moviereview/*.java; then
    echo "✗ 编译失败"
    echo "请检查Java源码和Hadoop类路径"
    exit 1
else
    echo "✓ 编译成功"
fi

echo "创建JAR包..."
cd build
if ! jar cf movie-review-analysis.jar com/moviereview/*.class; then
    echo "✗ JAR包创建失败"
    exit 1
else
    echo "✓ JAR包创建成功"
    echo "JAR包内容:"
    jar tf movie-review-analysis.jar
fi

cd "$PROJECT_DIR"

echo
echo "[5/8] 准备输入数据..."
echo "===================="

echo "检查原始数据文件..."
if [ ! -f "$PROJECT_DIR/data/ratings.csv" ]; then
    echo "✗ 找不到原始数据文件: $PROJECT_DIR/data/ratings.csv"
    echo "请确保ratings.csv文件已传输到虚拟机"
    exit 1
else
    echo "✓ 找到原始数据文件"
    echo "文件大小: $(ls -lh $PROJECT_DIR/data/ratings.csv | awk '{print $5}')"
fi

echo "处理数据（去除标题行）..."
if ! tail -n +2 $PROJECT_DIR/data/ratings.csv > data/clean_ratings.csv; then
    echo "✗ 数据处理失败"
    exit 1
else
    echo "✓ 数据处理成功"
    echo "处理后文件大小: $(ls -lh data/clean_ratings.csv | awk '{print $5}')"
fi

echo "数据样本:"
head -3 data/clean_ratings.csv

echo
echo "[6/8] 上传数据到HDFS..."
echo "====================="

echo "创建HDFS输入目录..."
hdfs dfs -mkdir -p /user/input/moviereviews

echo "删除已存在的输入文件（如果有）..."
hdfs dfs -rm -f /user/input/moviereviews/clean_ratings.csv 2>/dev/null

echo "上传数据文件到HDFS..."
if ! hdfs dfs -put data/clean_ratings.csv /user/input/moviereviews/; then
    echo "✗ 数据上传失败"
    echo "请检查HDFS状态和权限"
    exit 1
else
    echo "✓ 数据上传成功"
fi

echo "验证HDFS中的数据..."
hdfs dfs -ls /user/input/moviereviews
hdfs dfs -cat /user/input/moviereviews/clean_ratings.csv | head -3

echo
echo "[7/8] 运行MapReduce作业..."
echo "========================"

echo "清理输出目录..."
hdfs dfs -rm -r /user/output/moviereviews 2>/dev/null

echo "提交MapReduce作业..."
echo "作业参数:"
echo "  输入路径: /user/input/moviereviews"
echo "  输出路径: /user/output/moviereviews"
echo "  JAR文件: build/movie-review-analysis.jar"
echo "  主类: com.moviereview.MovieReviewAnalysis"
echo

if ! hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews; then
    echo "✗ MapReduce作业失败"
    echo "请检查作业日志获取详细错误信息"
    echo "可以访问虚拟机IP:8088查看作业状态"
    exit 1
else
    echo "✓ MapReduce作业成功完成"
fi

echo
echo "[8/8] 下载和分析结果..."
echo "====================="

echo "检查输出目录..."
hdfs dfs -ls /user/output/moviereviews

echo "下载结果到本地..."
if ! hdfs dfs -getmerge /user/output/moviereviews/part-r-* movie_analysis_result.txt; then
    echo "✗ 结果下载失败"
    exit 1
else
    echo "✓ 结果下载成功"
    echo "结果文件大小: $(ls -lh movie_analysis_result.txt | awk '{print $5}')"
fi

echo
echo "结果分析:"
echo "========="

echo "前10行结果:"
head -10 movie_analysis_result.txt

echo
echo "统计信息:"
echo "处理的电影总数: $(wc -l < movie_analysis_result.txt)"

echo
echo "========================================"
echo "部署完成！"
echo "========================================"
echo
echo "结果文件: $PROJECT_DIR/movie_analysis_result.txt"
echo
echo "Web界面:"
echo "  NameNode Web UI: http://虚拟机IP:9870"
echo "  ResourceManager Web UI: http://虚拟机IP:8088"
echo
echo "可以使用以下命令查看详细结果:"
echo "  cat movie_analysis_result.txt"
echo "  sort -k2 -nr movie_analysis_result.txt | head -10  # 评分最高的10部电影"
echo "  sort -k2 -n movie_analysis_result.txt | head -10   # 评分最低的10部电影"
echo
echo "可以使用以下命令停止Hadoop服务:"
echo "  stop-yarn.sh"
echo "  stop-dfs.sh"
echo
```

### 验证部署脚本 - verify_deployment.sh

**文件路径**：`/root/movie-review-analysis/verify_deployment.sh`

（详细内容请参考已创建的verify_deployment.sh文件）

## 🎯 完整执行流程

### 第一步：文件传输

请参考 `文件传输指导.md` 将以下文件传输到虚拟机：

1. **Java源码文件** → `/root/movie-review-analysis/src/com/moviereview/`
2. **数据文件** → `/root/ratings.csv`
3. **脚本文件** → `/root/movie-review-analysis/`

### 第二步：设置脚本权限

**当前路径**：`/root/movie-review-analysis`

```bash
# 设置脚本执行权限
chmod +x *.sh

# 验证权限
ls -la *.sh
```

### 第三步：环境检查

**当前路径**：`/root/movie-review-analysis`

```bash
# 运行环境检查
./check_environment.sh
```

### 第四步：一键部署

**当前路径**：`/root/movie-review-analysis`

```bash
# 运行完整部署
./deploy_complete.sh
```

### 第五步：验证结果

**当前路径**：`/root/movie-review-analysis`

```bash
# 验证部署结果
./verify_deployment.sh
```

## 📋 故障排除指南

### 常见问题1：虚拟机内存不足

**症状**：Hadoop服务启动失败，出现内存相关错误

**解决方案**：
```bash
# 检查内存使用
free -h

# 调整虚拟机内存到至少2GB
# 在虚拟机设置中增加内存分配

# 或者调整Hadoop内存配置
vim $HADOOP_HOME/etc/hadoop/yarn-site.xml
# 添加以下配置：
# <property>
#   <name>yarn.nodemanager.resource.memory-mb</name>
#   <value>1024</value>
# </property>
```

### 常见问题2：网络连接问题

**症状**：无法访问Web界面

**解决方案**：
```bash
# 检查防火墙状态
systemctl status firewalld

# 开放必要端口
firewall-cmd --permanent --add-port=9870/tcp
firewall-cmd --permanent --add-port=8088/tcp
firewall-cmd --reload

# 或者临时关闭防火墙（仅用于测试）
systemctl stop firewalld
```

### 常见问题3：磁盘空间不足

**症状**：HDFS操作失败，磁盘空间不足

**解决方案**：
```bash
# 检查磁盘使用
df -h

# 清理不必要的文件
yum clean all
rm -rf /tmp/*

# 扩展虚拟机磁盘（在虚拟机设置中）
```

### 常见问题4：Java版本不兼容

**症状**：编译或运行时出现Java版本错误

**解决方案**：
```bash
# 检查Java版本
java -version
javac -version

# 如果版本不匹配，重新安装
yum remove -y java-*
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 重新设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
```

## 📊 性能优化建议

### 优化1：调整JVM内存

**当前路径**：`/opt/hadoop/current/etc/hadoop`

```bash
# 编辑hadoop-env.sh
vim hadoop-env.sh

# 添加JVM内存配置
export HADOOP_HEAPSIZE=1024
export YARN_HEAPSIZE=1024
```

### 优化2：调整并发参数

**当前路径**：`/root/movie-review-analysis`

```bash
# 运行作业时指定参数
hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis \
  -Dmapreduce.job.maps=2 \
  -Dmapreduce.job.reduces=1 \
  -Dmapreduce.map.memory.mb=512 \
  -Dmapreduce.reduce.memory.mb=1024 \
  /user/input/moviereviews /user/output/moviereviews
```

### 优化3：启用压缩

```bash
# 启用中间结果压缩
hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis \
  -Dmapreduce.map.output.compress=true \
  -Dmapreduce.map.output.compress.codec=org.apache.hadoop.io.compress.DefaultCodec \
  /user/input/moviereviews /user/output/moviereviews
```

## 🎯 验收标准

### 成功标准检查清单

**环境验证**：
- [ ] CentOS系统正常运行
- [ ] Java 8环境配置正确
- [ ] Hadoop服务全部启动（5个进程）
- [ ] SSH免密登录配置成功

**网络验证**：
- [ ] 虚拟机网络连接正常
- [ ] Web界面可从主机访问
- [ ] 防火墙配置正确

**项目验证**：
- [ ] 所有文件成功传输
- [ ] Java代码编译成功
- [ ] JAR包包含所有类文件
- [ ] 数据成功上传到HDFS

**作业验证**：
- [ ] MapReduce作业成功完成
- [ ] 输出目录包含结果文件
- [ ] 结果格式正确
- [ ] 结果数量合理

**性能验证**：
- [ ] Combiner减少了网络传输
- [ ] 作业完成时间合理
- [ ] 资源使用正常
- [ ] 无错误或警告信息

## 📝 最终检查命令

**当前路径**：`/root/movie-review-analysis`

```bash
echo "========================================"
echo "最终验收检查"
echo "========================================"

echo "[1] 系统信息："
uname -a
cat /etc/centos-release

echo "[2] Hadoop服务状态："
jps

echo "[3] HDFS状态："
hdfs dfsadmin -report | grep "Live datanodes"

echo "[4] 输入数据："
hdfs dfs -ls /user/input/moviereviews

echo "[5] 输出结果："
hdfs dfs -ls /user/output/moviereviews

echo "[6] 结果样本："
head -10 movie_analysis_result.txt

echo "[7] 结果统计："
wc -l movie_analysis_result.txt

echo "[8] 虚拟机IP："
hostname -I

echo "========================================"
echo "检查完成"
echo "========================================"
```

## 🎉 部署完成

恭喜！您已经成功完成了电影评分分析项目在CentOS虚拟机上的Hadoop集群部署。

**项目成果**：
- ✅ 完整的CentOS虚拟机Hadoop环境
- ✅ 成功运行的MapReduce作业
- ✅ 大数据处理结果分析
- ✅ 性能优化验证

**Web界面访问**：
- **NameNode**: http://虚拟机IP:9870
- **ResourceManager**: http://虚拟机IP:8088

**下一步建议**：
1. 尝试处理更大规模的数据集
2. 实现更复杂的分析功能
3. 学习Spark等更高级的大数据技术
4. 部署到真实的分布式集群环境

**技术收获**：
- 掌握了CentOS系统的Hadoop部署
- 理解了虚拟机环境的配置和优化
- 学会了Linux环境下的大数据开发
- 具备了分布式计算的实践经验
```
