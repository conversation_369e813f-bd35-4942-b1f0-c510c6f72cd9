package com.moviereview;

import java.io.*;
import java.util.*;

/**
 * 快速测试类 - 只处理前1000行数据
 */
public class QuickTest {
    
    public static void main(String[] args) {
        System.out.println("=== 电影评分分析 - 快速测试 ===");
        
        String inputFile = "ratings.csv";
        
        // 检查文件是否存在
        File file = new File(inputFile);
        if (!file.exists()) {
            System.err.println("错误：找不到文件 " + inputFile);
            System.err.println("当前目录: " + System.getProperty("user.dir"));
            
            // 尝试其他可能的路径
            String[] possiblePaths = {
                "..\\ratings.csv",
                "..\\..\\ratings.csv",
                "ratings.csv"
            };
            
            for (String path : possiblePaths) {
                File testFile = new File(path);
                if (testFile.exists()) {
                    System.out.println("找到文件: " + path);
                    inputFile = path;
                    file = testFile;
                    break;
                }
            }
            
            if (!file.exists()) {
                System.err.println("无法找到ratings.csv文件");
                return;
            }
        }
        
        System.out.println("数据文件: " + inputFile);
        System.out.println("文件大小: " + String.format("%.2f MB", file.length() / 1024.0 / 1024.0));
        
        try {
            // 读取并处理数据（限制1000行）
            Map<String, List<Double>> movieRatings = new HashMap<>();
            int totalLines = 0;
            int validLines = 0;
            int invalidLines = 0;
            int maxLines = 1000; // 限制处理行数
            
            System.out.println("\n=== 开始处理数据（限制" + maxLines + "行）===");
            
            try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                String line;
                boolean isFirstLine = true;
                
                while ((line = reader.readLine()) != null && totalLines < maxLines) {
                    totalLines++;
                    
                    // 跳过标题行
                    if (isFirstLine) {
                        isFirstLine = false;
                        System.out.println("标题行: " + line);
                        continue;
                    }
                    
                    // 处理数据行
                    try {
                        String[] fields = parseCSVLine(line);
                        if (fields.length >= 4) {
                            String movieId = cleanField(fields[2]);
                            String ratingStr = cleanField(fields[3]);
                            
                            if (!movieId.isEmpty() && !ratingStr.isEmpty()) {
                                double rating = Double.parseDouble(ratingStr);
                                if (rating >= 1.0 && rating <= 5.0) {
                                    movieRatings.computeIfAbsent(movieId, k -> new ArrayList<>()).add(rating);
                                    validLines++;
                                } else {
                                    invalidLines++;
                                }
                            } else {
                                invalidLines++;
                            }
                        } else {
                            invalidLines++;
                        }
                    } catch (Exception e) {
                        invalidLines++;
                    }
                    
                    // 每处理100行显示进度
                    if (totalLines % 100 == 0) {
                        System.out.println("已处理: " + totalLines + " 行");
                    }
                }
            }
            
            System.out.println("\n=== 数据处理统计 ===");
            System.out.println("总行数: " + totalLines);
            System.out.println("有效行数: " + validLines);
            System.out.println("无效行数: " + invalidLines);
            System.out.println("有效率: " + String.format("%.2f%%", (double)validLines/totalLines*100));
            System.out.println("处理的电影数量: " + movieRatings.size());
            
            // 显示前几行原始数据
            System.out.println("\n=== 数据样本 ===");
            try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                for (int i = 0; i < 5; i++) {
                    String line = reader.readLine();
                    if (line != null) {
                        System.out.println("第" + (i+1) + "行: " + line);
                    }
                }
            }
            
            // 计算平均评分
            System.out.println("\n=== 计算平均评分 ===");
            Map<String, Double> averageRatings = new HashMap<>();
            
            for (Map.Entry<String, List<Double>> entry : movieRatings.entrySet()) {
                String movieId = entry.getKey();
                List<Double> ratings = entry.getValue();
                
                double sum = ratings.stream().mapToDouble(Double::doubleValue).sum();
                double average = sum / ratings.size();
                averageRatings.put(movieId, Math.round(average * 100.0) / 100.0);
            }
            
            System.out.println("计算完成的电影数量: " + averageRatings.size());
            
            // 显示前10个结果
            System.out.println("\n=== 前10个电影的平均评分 ===");
            averageRatings.entrySet().stream()
                .limit(10)
                .forEach(entry -> System.out.println(entry.getKey() + "\t" + String.format("%.2f", entry.getValue())));
            
            // 模拟Combiner效果
            if (validLines > 0) {
                int originalRecords = validLines;
                int combinedRecords = averageRatings.size();
                double compressionRatio = (double)(originalRecords - combinedRecords) / originalRecords * 100;
                
                System.out.println("\n=== Combiner优化效果 ===");
                System.out.println("原始记录数: " + originalRecords);
                System.out.println("聚合后记录数: " + combinedRecords);
                System.out.println("数据压缩比: " + String.format("%.2f%%", compressionRatio));
            }
            
            System.out.println("\n=== 快速测试完成 ===");
            System.out.println("数据处理成功！MapReduce逻辑验证通过。");
            
        } catch (IOException e) {
            System.err.println("文件读取错误: " + e.getMessage());
        }
    }
    
    // 解析CSV行
    private static String[] parseCSVLine(String line) {
        return line.split(",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)");
    }
    
    // 清理字段
    private static String cleanField(String field) {
        if (field == null) return "";
        return field.replace("\"", "").trim();
    }
}
