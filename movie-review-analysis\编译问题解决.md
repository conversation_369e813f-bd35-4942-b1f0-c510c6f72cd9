# 编译问题解决指南

## 🚨 问题分析

错误信息显示找不到Hadoop相关的包，说明HADOOP_CLASSPATH环境变量没有正确设置。

## 🔧 解决步骤

### 第一步：检查Hadoop安装

```bash
# 检查Hadoop是否正确安装
echo $HADOOP_HOME
ls -la $HADOOP_HOME

# 检查Hadoop版本
hadoop version

# 检查Hadoop目录结构
ls -la $HADOOP_HOME/share/hadoop/
```

### 第二步：正确设置HADOOP_CLASSPATH

```bash
# 方法1：使用hadoop classpath命令（推荐）
export HADOOP_CLASSPATH=$(hadoop classpath)
echo $HADOOP_CLASSPATH

# 方法2：手动设置完整路径
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/hadoop-common-*.jar:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/hadoop-hdfs-*.jar:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/hadoop-mapreduce-client-core-*.jar:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/hadoop-yarn-common-*.jar:$HADOOP_HOME/share/hadoop/yarn/lib/*

# 验证类路径设置
echo "HADOOP_CLASSPATH长度: ${#HADOOP_CLASSPATH}"
```

### 第三步：重新编译

```bash
# 进入项目目录
cd /root/movie-review-analysis

# 清理构建目录
rm -rf build/*
mkdir -p build

# 使用正确的类路径编译
javac -cp $(hadoop classpath) -d build src/com/moviereview/*.java

# 如果上面命令失败，尝试这个
javac -cp "$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*" -d build src/com/moviereview/*.java
```

### 第四步：验证编译结果

```bash
# 检查编译结果
ls -la build/com/moviereview/
# 应该看到5个.class文件

# 创建JAR包
cd build
jar cf movie-review-analysis.jar com/moviereview/*.class

# 验证JAR包
jar tf movie-review-analysis.jar

# 返回项目根目录
cd /root/movie-review-analysis
```

## 🔍 详细诊断步骤

### 检查1：Hadoop安装完整性

```bash
# 检查关键JAR文件是否存在
ls -la $HADOOP_HOME/share/hadoop/common/hadoop-common-*.jar
ls -la $HADOOP_HOME/share/hadoop/mapreduce/hadoop-mapreduce-client-core-*.jar
ls -la $HADOOP_HOME/share/hadoop/hdfs/hadoop-hdfs-*.jar
ls -la $HADOOP_HOME/share/hadoop/yarn/hadoop-yarn-common-*.jar
```

### 检查2：环境变量

```bash
# 检查所有相关环境变量
echo "JAVA_HOME: $JAVA_HOME"
echo "HADOOP_HOME: $HADOOP_HOME"
echo "HADOOP_CONF_DIR: $HADOOP_CONF_DIR"
echo "PATH: $PATH"
```

### 检查3：Java版本兼容性

```bash
# 检查Java版本
java -version
javac -version

# 确保使用Java 8
alternatives --list | grep java
```

## 🛠️ 替代编译方法

### 方法1：使用完整路径编译

```bash
# 创建编译脚本
cat > compile.sh << 'EOF'
#!/bin/bash

# 设置变量
HADOOP_HOME=/opt/hadoop/current
PROJECT_DIR=/root/movie-review-analysis

# 进入项目目录
cd $PROJECT_DIR

# 清理构建目录
rm -rf build/*
mkdir -p build

# 设置完整的Hadoop类路径
HADOOP_JARS=""
for jar in $HADOOP_HOME/share/hadoop/common/*.jar; do
    HADOOP_JARS="$HADOOP_JARS:$jar"
done
for jar in $HADOOP_HOME/share/hadoop/common/lib/*.jar; do
    HADOOP_JARS="$HADOOP_JARS:$jar"
done
for jar in $HADOOP_HOME/share/hadoop/hdfs/*.jar; do
    HADOOP_JARS="$HADOOP_JARS:$jar"
done
for jar in $HADOOP_HOME/share/hadoop/hdfs/lib/*.jar; do
    HADOOP_JARS="$HADOOP_JARS:$jar"
done
for jar in $HADOOP_HOME/share/hadoop/mapreduce/*.jar; do
    HADOOP_JARS="$HADOOP_JARS:$jar"
done
for jar in $HADOOP_HOME/share/hadoop/mapreduce/lib/*.jar; do
    HADOOP_JARS="$HADOOP_JARS:$jar"
done
for jar in $HADOOP_HOME/share/hadoop/yarn/*.jar; do
    HADOOP_JARS="$HADOOP_JARS:$jar"
done
for jar in $HADOOP_HOME/share/hadoop/yarn/lib/*.jar; do
    HADOOP_JARS="$HADOOP_JARS:$jar"
done

# 去掉开头的冒号
HADOOP_JARS=${HADOOP_JARS:1}

echo "编译Java源码..."
javac -cp "$HADOOP_JARS" -d build src/com/moviereview/*.java

if [ $? -eq 0 ]; then
    echo "编译成功！"
    cd build
    jar cf movie-review-analysis.jar com/moviereview/*.class
    echo "JAR包创建完成"
    jar tf movie-review-analysis.jar
else
    echo "编译失败！"
fi
EOF

# 执行编译脚本
chmod +x compile.sh
./compile.sh
```

### 方法2：使用Hadoop提供的编译工具

```bash
# 使用hadoop命令编译
hadoop com.sun.tools.javac.Main -cp $(hadoop classpath) -d build src/com/moviereview/*.java
```

## 🔄 完整的重新编译流程

```bash
# 1. 重新加载环境变量
source ~/.bashrc

# 2. 验证Hadoop环境
hadoop version
echo $HADOOP_HOME

# 3. 进入项目目录
cd /root/movie-review-analysis

# 4. 清理并重新创建构建目录
rm -rf build
mkdir -p build

# 5. 使用hadoop classpath命令获取类路径
CLASSPATH=$(hadoop classpath)
echo "使用的类路径: $CLASSPATH"

# 6. 编译Java源码
javac -cp "$CLASSPATH" -d build src/com/moviereview/*.java

# 7. 检查编译结果
if [ $? -eq 0 ]; then
    echo "✓ 编译成功"
    ls -la build/com/moviereview/
    
    # 8. 创建JAR包
    cd build
    jar cf movie-review-analysis.jar com/moviereview/*.class
    echo "✓ JAR包创建成功"
    jar tf movie-review-analysis.jar
    cd ..
else
    echo "✗ 编译失败，请检查错误信息"
fi
```

## 📝 常见错误及解决方案

### 错误1：找不到hadoop命令
```bash
# 解决方案：重新设置PATH
export PATH=$PATH:/opt/hadoop/current/bin:/opt/hadoop/current/sbin
```

### 错误2：权限问题
```bash
# 解决方案：检查文件权限
chmod -R 755 /opt/hadoop/current
chmod -R 755 /root/movie-review-analysis
```

### 错误3：Java版本不兼容
```bash
# 解决方案：确保使用Java 8
yum install -y java-1.8.0-openjdk-devel
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
```

## ✅ 验证编译成功

编译成功后应该看到：
- `build/com/moviereview/` 目录下有5个.class文件
- `build/movie-review-analysis.jar` 文件存在
- JAR包包含所有必要的类文件

```bash
# 最终验证命令
ls -la build/com/moviereview/*.class
ls -la build/movie-review-analysis.jar
jar tf build/movie-review-analysis.jar | grep -c "\.class"
# 应该输出5，表示包含5个类文件
```
