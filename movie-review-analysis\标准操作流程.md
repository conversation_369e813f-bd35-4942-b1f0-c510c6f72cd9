# 电影评分分析项目 - 标准操作流程

## 📋 项目信息

- **项目路径**：`/root/movie-review-analysis`
- **数据文件**：`/root/movie-review-analysis/ratings.csv`
- **编译方式**：标准javac编译

## 🎯 前置条件

- CentOS虚拟机已安装并运行
- Java 8已安装
- Hadoop已安装并配置
- SSH免密登录已配置
- 项目文件已传输到虚拟机

## 🚀 标准操作流程

### 第一步：启动Hadoop服务

```bash
# 进入Hadoop目录
cd /opt/hadoop/current

# 启动HDFS服务
start-dfs.sh

# 启动YARN服务  
start-yarn.sh

# 验证服务状态
jps
# 应该看到：NameNode、DataNode、ResourceManager、NodeManager、SecondaryNameNode
```

### 第二步：进入项目目录

```bash
# 进入项目目录
cd /root/movie-review-analysis

# 检查项目文件
ls -la
# 应该看到：src/ build/ ratings.csv

# 检查Java源码
ls -la src/com/moviereview/
# 应该看到5个.java文件
```

### 第三步：编译项目

```bash
# 设置Hadoop类路径
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*

# 清理构建目录
rm -rf build/*
mkdir -p build

# 编译Java源码
javac -cp $HADOOP_CLASSPATH -d build src/com/moviereview/*.java

# 检查编译结果
ls -la build/com/moviereview/
# 应该看到5个.class文件

# 创建JAR包
cd build
jar cf movie-review-analysis.jar com/moviereview/*.class

# 验证JAR包
jar tf movie-review-analysis.jar

# 返回项目根目录
cd /root/movie-review-analysis
```

### 第四步：准备输入数据

```bash
# 处理原始数据（去除标题行）
tail -n +2 ratings.csv > clean_ratings.csv

# 查看处理后的数据
head -5 clean_ratings.csv
echo "数据行数："
wc -l clean_ratings.csv
```

### 第五步：上传数据到HDFS

```bash
# 创建HDFS输入目录
hdfs dfs -mkdir -p /user/input/moviereviews

# 删除已存在的文件（如果有）
hdfs dfs -rm -f /user/input/moviereviews/clean_ratings.csv

# 上传数据文件到HDFS
hdfs dfs -put clean_ratings.csv /user/input/moviereviews/

# 验证上传成功
hdfs dfs -ls /user/input/moviereviews
hdfs dfs -cat /user/input/moviereviews/clean_ratings.csv | head -3
```

### 第六步：运行MapReduce作业

```bash
# 清理输出目录
hdfs dfs -rm -r /user/output/moviereviews

# 提交MapReduce作业
hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews

# 等待作业完成，观察输出信息
```

### 第七步：查看和分析结果

```bash
# 查看输出目录
hdfs dfs -ls /user/output/moviereviews

# 查看结果内容（前20行）
hdfs dfs -cat /user/output/moviereviews/part-r-* | head -20

# 下载结果到本地
hdfs dfs -getmerge /user/output/moviereviews/part-r-* movie_analysis_result.txt

# 查看本地结果文件
head -10 movie_analysis_result.txt

# 统计分析
echo "=== 结果统计 ==="
echo "处理的电影总数："
wc -l movie_analysis_result.txt

echo "评分最高的10部电影："
sort -k2 -nr movie_analysis_result.txt | head -10

echo "评分最低的10部电影："
sort -k2 -n movie_analysis_result.txt | head -10

echo "评分分布统计："
awk '{
    if($2 >= 4.0) high++
    else if($2 >= 3.0) medium++
    else low++
} END {
    print "高评分(4.0+): " high " 部"
    print "中评分(3.0-3.9): " medium " 部"  
    print "低评分(<3.0): " low " 部"
}' movie_analysis_result.txt
```

## 🌐 Web界面访问

在主机浏览器中访问：
- **NameNode Web UI**: `http://虚拟机IP:9870`
- **ResourceManager Web UI**: `http://虚拟机IP:8088`

## 🔧 停止服务

```bash
# 停止YARN服务
stop-yarn.sh

# 停止HDFS服务
stop-dfs.sh

# 验证服务已停止
jps
```

## 📝 常用命令

### HDFS操作
```bash
# 查看HDFS目录
hdfs dfs -ls /

# 查看HDFS状态
hdfs dfsadmin -report

# 删除HDFS文件
hdfs dfs -rm /path/to/file

# 删除HDFS目录
hdfs dfs -rm -r /path/to/directory
```

### 作业监控
```bash
# 查看正在运行的应用
yarn application -list

# 查看应用状态
yarn application -status application_id

# 查看应用日志
yarn logs -applicationId application_id
```

### 系统监控
```bash
# 查看Java进程
jps

# 查看系统资源
free -h
df -h

# 查看网络端口
netstat -tuln | grep -E "9000|9870|8088"
```

## ⚠️ 常见问题

### 问题1：编译失败
```bash
# 检查Java版本
java -version
javac -version

# 检查Hadoop类路径
echo $HADOOP_CLASSPATH

# 重新设置环境变量
source ~/.bashrc
```

### 问题2：HDFS操作失败
```bash
# 检查HDFS服务状态
jps | grep -E "NameNode|DataNode"

# 重启HDFS服务
stop-dfs.sh
start-dfs.sh
```

### 问题3：作业提交失败
```bash
# 检查YARN服务状态
jps | grep -E "ResourceManager|NodeManager"

# 检查JAR包
jar tf build/movie-review-analysis.jar

# 重启YARN服务
stop-yarn.sh
start-yarn.sh
```

### 问题4：Web界面无法访问
```bash
# 检查防火墙
systemctl status firewalld

# 临时关闭防火墙
systemctl stop firewalld

# 检查端口监听
netstat -tuln | grep -E "9870|8088"
```

## ✅ 成功标准

- [ ] Hadoop服务正常启动（5个Java进程）
- [ ] 项目编译成功（生成JAR包）
- [ ] 数据成功上传到HDFS
- [ ] MapReduce作业成功完成
- [ ] 生成正确的结果文件
- [ ] Web界面可正常访问
- [ ] 结果分析合理

## 📊 预期结果

- 处理数万条评分记录
- 生成数千部电影的平均评分
- 评分范围在1.0-5.0之间
- Combiner优化减少网络传输
- 作业执行时间在几分钟内完成
