# 电影评分分析项目 - CentOS虚拟机简化部署教程

## 📋 教程概述

本教程提供了在CentOS虚拟机上部署和运行电影评分分析MapReduce项目的标准操作步骤。

## 🎯 环境信息

- **操作系统**：CentOS 7/8
- **项目路径**：`/root/movie-review-analysis`
- **数据文件路径**：`/root/movie-review-analysis/ratings.csv`
- **Hadoop安装路径**：`/opt/hadoop/current`
- **Java安装路径**：`/usr/lib/jvm/java-1.8.0-openjdk`

## 🚀 第一阶段：系统环境准备

### 步骤1：安装Java和基础工具

**当前路径**：`/root`

```bash
# 更新系统
yum update -y

# 安装Java 8和必要工具
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel wget vim

# 验证Java安装
java -version
javac -version

# 设置Java环境变量
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
echo 'export PATH=$PATH:$JAVA_HOME/bin' >> ~/.bashrc
source ~/.bashrc
```

### 步骤2：下载和安装Hadoop

**当前路径**：`/root`

```bash
# 创建Hadoop安装目录
mkdir -p /opt/hadoop
cd /opt/hadoop

# 下载Hadoop 3.3.4
wget https://archive.apache.org/dist/hadoop/common/hadoop-3.3.4/hadoop-3.3.4.tar.gz

# 解压Hadoop
tar -xzf hadoop-3.3.4.tar.gz

# 创建软链接
ln -s hadoop-3.3.4 current

# 设置Hadoop环境变量
echo 'export HADOOP_HOME=/opt/hadoop/current' >> ~/.bashrc
echo 'export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop' >> ~/.bashrc
echo 'export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin' >> ~/.bashrc
source ~/.bashrc

# 验证Hadoop安装
hadoop version
```

### 步骤3：配置SSH免密登录

**当前路径**：`/root`

```bash
# 安装SSH服务
yum install -y openssh-server openssh-clients

# 启动SSH服务
systemctl start sshd
systemctl enable sshd

# 生成SSH密钥
ssh-keygen -t rsa -P '' -f ~/.ssh/id_rsa

# 配置免密登录
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
chmod 0600 ~/.ssh/authorized_keys

# 测试SSH连接
ssh localhost exit
```

## 🔧 第二阶段：Hadoop配置

### 步骤4：配置Hadoop核心文件

**当前路径**：`/opt/hadoop/current/etc/hadoop`

```bash
cd /opt/hadoop/current/etc/hadoop

# 配置hadoop-env.sh
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> hadoop-env.sh

# 配置core-site.xml
cat > core-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>fs.defaultFS</name>
        <value>hdfs://localhost:9000</value>
    </property>
    <property>
        <name>hadoop.tmp.dir</name>
        <value>/opt/hadoop/data/tmp</value>
    </property>
</configuration>
EOF

# 配置hdfs-site.xml
cat > hdfs-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>dfs.replication</name>
        <value>1</value>
    </property>
    <property>
        <name>dfs.namenode.name.dir</name>
        <value>/opt/hadoop/data/namenode</value>
    </property>
    <property>
        <name>dfs.datanode.data.dir</name>
        <value>/opt/hadoop/data/datanode</value>
    </property>
    <property>
        <name>dfs.namenode.http-address</name>
        <value>0.0.0.0:9870</value>
    </property>
    <property>
        <name>dfs.permissions.enabled</name>
        <value>false</value>
    </property>
</configuration>
EOF

# 配置mapred-site.xml
cat > mapred-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>mapreduce.framework.name</name>
        <value>yarn</value>
    </property>
    <property>
        <name>mapreduce.application.classpath</name>
        <value>$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*</value>
    </property>
</configuration>
EOF

# 配置yarn-site.xml
cat > yarn-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
    </property>
    <property>
        <name>yarn.nodemanager.aux-services.mapreduce.shuffle.class</name>
        <value>org.apache.hadoop.mapred.ShuffleHandler</value>
    </property>
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>localhost</value>
    </property>
    <property>
        <name>yarn.resourcemanager.webapp.address</name>
        <value>0.0.0.0:8088</value>
    </property>
    <property>
        <name>yarn.nodemanager.vmem-check-enabled</name>
        <value>false</value>
    </property>
</configuration>
EOF
```

### 步骤5：创建数据目录并格式化NameNode

**当前路径**：`/opt/hadoop`

```bash
# 创建数据目录
mkdir -p /opt/hadoop/data/{tmp,namenode,datanode}
chmod 755 /opt/hadoop/data/*

# 格式化NameNode（仅第一次需要）
hdfs namenode -format
```

## 🚀 第三阶段：启动Hadoop集群

### 步骤6：启动Hadoop服务

**当前路径**：`/opt/hadoop/current`

```bash
# 启动HDFS服务
start-dfs.sh

# 启动YARN服务
start-yarn.sh

# 验证服务状态
jps
# 应该看到：NameNode、DataNode、ResourceManager、NodeManager、SecondaryNameNode

# 测试HDFS
hdfs dfs -mkdir -p /user/root
hdfs dfs -ls /
```

## 📁 第四阶段：项目部署

### 步骤7：创建项目目录和传输文件

**当前路径**：`/root`

```bash
# 创建项目目录
mkdir -p /root/movie-review-analysis/src/com/moviereview
mkdir -p /root/movie-review-analysis/build
cd /root/movie-review-analysis
```

**需要传输的文件到虚拟机**：

1. **Java源码文件** → `/root/movie-review-analysis/src/com/moviereview/`
   - MovieReviewAnalysis.java
   - MovieRatingMapper.java
   - MovieRatingCombiner.java
   - MovieRatingPartitioner.java
   - MovieRatingReducer.java

2. **数据文件** → `/root/movie-review-analysis/`
   - ratings.csv

**使用SCP传输文件（从Windows执行）**：
```bash
# 传输Java源码文件
scp D:\ldlhadoopxhd\movie-review-analysis\src\com\moviereview\*.java root@虚拟机IP:/root/movie-review-analysis/src/com/moviereview/

# 传输数据文件
scp D:\ldlhadoopxhd\ratings.csv root@虚拟机IP:/root/movie-review-analysis/
```

### 步骤8：验证文件传输

**当前路径**：`/root/movie-review-analysis`

```bash
# 检查文件
ls -la src/com/moviereview/    # 应该看到5个.java文件
ls -la ratings.csv             # 应该看到数据文件
head -5 ratings.csv            # 查看数据样本
```

## 🔨 第五阶段：编译和运行

### 步骤9：编译MapReduce项目

**当前路径**：`/root/movie-review-analysis`

```bash
# 设置Hadoop类路径
export HADOOP_CLASSPATH=$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*:$HADOOP_HOME/share/hadoop/hdfs/*:$HADOOP_HOME/share/hadoop/hdfs/lib/*:$HADOOP_HOME/share/hadoop/mapreduce/*:$HADOOP_HOME/share/hadoop/mapreduce/lib/*:$HADOOP_HOME/share/hadoop/yarn/*:$HADOOP_HOME/share/hadoop/yarn/lib/*

# 编译Java源码
javac -cp $HADOOP_CLASSPATH -d build src/com/moviereview/*.java

# 检查编译结果
ls -la build/com/moviereview/

# 创建JAR包
cd build
jar cf movie-review-analysis.jar com/moviereview/*.class

# 验证JAR包
jar tf movie-review-analysis.jar

# 返回项目根目录
cd /root/movie-review-analysis
```

### 步骤10：准备输入数据

**当前路径**：`/root/movie-review-analysis`

```bash
# 处理数据（去除标题行）
tail -n +2 ratings.csv > clean_ratings.csv

# 查看处理后的数据
head -5 clean_ratings.csv
wc -l clean_ratings.csv
```

### 步骤11：上传数据到HDFS

**当前路径**：`/root/movie-review-analysis`

```bash
# 创建HDFS输入目录
hdfs dfs -mkdir -p /user/input/moviereviews

# 上传数据文件到HDFS
hdfs dfs -put clean_ratings.csv /user/input/moviereviews/

# 验证上传成功
hdfs dfs -ls /user/input/moviereviews
hdfs dfs -cat /user/input/moviereviews/clean_ratings.csv | head -5
```

### 步骤12：运行MapReduce作业

**当前路径**：`/root/movie-review-analysis`

```bash
# 确保输出目录不存在
hdfs dfs -rm -r /user/output/moviereviews

# 提交MapReduce作业
hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews

# 等待作业完成
```

### 步骤13：查看和分析结果

**当前路径**：`/root/movie-review-analysis`

```bash
# 查看输出目录
hdfs dfs -ls /user/output/moviereviews

# 查看结果内容
hdfs dfs -cat /user/output/moviereviews/part-r-* | head -20

# 下载结果到本地
hdfs dfs -getmerge /user/output/moviereviews/part-r-* movie_analysis_result.txt

# 分析结果
echo "处理的电影总数："
wc -l movie_analysis_result.txt

echo "评分最高的10部电影："
sort -k2 -nr movie_analysis_result.txt | head -10

echo "评分最低的10部电影："
sort -k2 -n movie_analysis_result.txt | head -10
```

## 🌐 Web界面访问

- **NameNode Web UI**: `http://虚拟机IP:9870`
- **ResourceManager Web UI**: `http://虚拟机IP:8088`

## 🔧 停止服务

```bash
# 停止Hadoop服务
stop-yarn.sh
stop-dfs.sh
```

## 📝 完整操作总结

1. **环境准备**：安装Java、Hadoop，配置SSH
2. **Hadoop配置**：配置核心文件，格式化NameNode
3. **启动服务**：启动HDFS和YARN
4. **文件传输**：传输Java源码和数据文件
5. **编译项目**：使用javac编译，创建JAR包
6. **数据处理**：处理数据并上传到HDFS
7. **运行作业**：提交MapReduce作业
8. **查看结果**：下载并分析结果

## ✅ 成功标准

- Hadoop服务正常运行（5个Java进程）
- Web界面可访问
- MapReduce作业成功完成
- 生成正确的分析结果文件
