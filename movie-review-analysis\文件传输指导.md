# 文件传输到CentOS虚拟机指导

## 📋 需要传输的文件

### Java源码文件（传输到虚拟机 `/root/movie-review-analysis/src/com/moviereview/`）
- MovieReviewAnalysis.java
- MovieRatingMapper.java  
- MovieRatingCombiner.java
- MovieRatingPartitioner.java
- MovieRatingReducer.java

### 数据文件（传输到虚拟机 `/root/movie-review-analysis/data/`）
- ratings.csv

### 脚本文件（传输到虚拟机 `/root/movie-review-analysis/`）
- check_environment.sh
- deploy_complete.sh
- verify_deployment.sh

## 🚀 传输方法

### 方法1：使用SCP命令（推荐）

#### 从Windows PowerShell执行：

```powershell
# 设置虚拟机IP地址（请替换为实际IP）
$VM_IP = "*************"

# 传输Java源码文件
scp D:\ldlhadoopxhd\movie-review-analysis\src\com\moviereview\*.java root@${VM_IP}:/root/movie-review-analysis/src/com/moviereview/

# 传输数据文件
scp D:\ldlhadoopxhd\ratings.csv root@${VM_IP}:/root/movie-review-analysis/data/

# 传输脚本文件
scp D:\ldlhadoopxhd\movie-review-analysis\*.sh root@${VM_IP}:/root/movie-review-analysis/

# 传输教程文档
scp "D:\ldlhadoopxhd\movie-review-analysis\CentOS虚拟机部署完整教程.md" root@${VM_IP}:/root/movie-review-analysis/
```

#### 从Linux/Mac终端执行：

```bash
# 设置虚拟机IP地址
VM_IP="*************"

# 传输Java源码文件
scp /path/to/movie-review-analysis/src/com/moviereview/*.java root@$VM_IP:/root/movie-review-analysis/src/com/moviereview/

# 传输数据文件
scp /path/to/ratings.csv root@$VM_IP:/root/movie-review-analysis/data/

# 传输脚本文件
scp /path/to/movie-review-analysis/*.sh root@$VM_IP:/root/movie-review-analysis/
```

### 方法2：使用WinSCP图形界面（Windows用户）

#### 步骤：
1. **下载安装WinSCP**
   - 访问：https://winscp.net/
   - 下载并安装WinSCP

2. **连接到虚拟机**
   - 打开WinSCP
   - 文件协议：SFTP
   - 主机名：虚拟机IP地址
   - 端口号：22
   - 用户名：root
   - 密码：虚拟机root密码

3. **传输文件**
   - 左侧：Windows本地文件
   - 右侧：虚拟机文件系统
   - 拖拽文件到对应目录

### 方法3：使用共享文件夹（VMware/VirtualBox）

#### VMware共享文件夹设置：

1. **VMware设置**
   ```
   虚拟机设置 → 选项 → 共享文件夹 → 启用共享文件夹
   添加共享文件夹：D:\ldlhadoopxhd → 名称：shared
   ```

2. **虚拟机中挂载**
   ```bash
   # 安装VMware Tools（如果未安装）
   yum install -y open-vm-tools
   
   # 创建挂载点
   mkdir -p /mnt/shared
   
   # 挂载共享文件夹
   vmhgfs-fuse .host:/shared /mnt/shared
   
   # 复制文件
   cp /mnt/shared/movie-review-analysis/src/com/moviereview/*.java /root/movie-review-analysis/src/com/moviereview/
   cp /mnt/shared/ratings.csv /root/movie-review-analysis/data/
   cp /mnt/shared/movie-review-analysis/*.sh /root/movie-review-analysis/
   ```

#### VirtualBox共享文件夹设置：

1. **VirtualBox设置**
   ```
   虚拟机设置 → 共享文件夹 → 添加共享文件夹
   文件夹路径：D:\ldlhadoopxhd
   文件夹名称：shared
   自动挂载：勾选
   ```

2. **虚拟机中访问**
   ```bash
   # 安装VirtualBox Guest Additions
   yum install -y kernel-devel kernel-headers gcc make perl
   
   # 挂载共享文件夹
   mkdir -p /mnt/shared
   mount -t vboxsf shared /mnt/shared
   
   # 复制文件
   cp /mnt/shared/movie-review-analysis/src/com/moviereview/*.java /root/movie-review-analysis/src/com/moviereview/
   cp /mnt/shared/ratings.csv /root/
   cp /mnt/shared/movie-review-analysis/*.sh /root/movie-review-analysis/
   ```

## 🔧 传输后验证

### 在虚拟机中执行以下命令验证文件传输：

```bash
# 检查项目目录结构
ls -la /root/movie-review-analysis/

# 检查Java源码文件
ls -la /root/movie-review-analysis/src/com/moviereview/
echo "应该看到5个.java文件"

# 检查数据文件
ls -la /root/ratings.csv
echo "应该看到ratings.csv文件，大小约350MB"

# 检查脚本文件
ls -la /root/movie-review-analysis/*.sh
echo "应该看到3个.sh脚本文件"

# 设置脚本执行权限
chmod +x /root/movie-review-analysis/*.sh

# 查看文件内容样本
head -5 /root/ratings.csv
head -10 /root/movie-review-analysis/src/com/moviereview/MovieReviewAnalysis.java
```

## 📝 获取虚拟机IP地址

### 在虚拟机中执行：

```bash
# 方法1：使用hostname命令
hostname -I

# 方法2：使用ip命令
ip addr show | grep "inet " | grep -v "127.0.0.1"

# 方法3：使用ifconfig命令
ifconfig | grep "inet " | grep -v "127.0.0.1"
```

### 常见网络配置：

- **NAT模式**：虚拟机IP通常是 192.168.x.x
- **桥接模式**：虚拟机IP与主机在同一网段
- **仅主机模式**：虚拟机IP通常是 192.168.56.x

## ⚠️ 常见问题解决

### 问题1：SSH连接被拒绝

```bash
# 在虚拟机中执行
systemctl start sshd
systemctl enable sshd

# 检查防火墙
systemctl status firewalld
firewall-cmd --permanent --add-service=ssh
firewall-cmd --reload
```

### 问题2：权限被拒绝

```bash
# 确保使用root用户
whoami

# 或者修改文件权限
chmod 755 /root/movie-review-analysis/
chmod 644 /root/movie-review-analysis/src/com/moviereview/*.java
```

### 问题3：文件编码问题

```bash
# 检查文件编码
file -i /root/ratings.csv

# 如果需要转换编码
iconv -f GBK -t UTF-8 /root/ratings.csv > /root/ratings_utf8.csv
mv /root/ratings_utf8.csv /root/ratings.csv
```

## ✅ 传输完成检查清单

- [ ] Java源码文件已传输（5个.java文件）
- [ ] 数据文件已传输（ratings.csv，约350MB）
- [ ] 脚本文件已传输（3个.sh文件）
- [ ] 脚本文件已设置执行权限
- [ ] 文件路径正确
- [ ] 文件内容完整
- [ ] 虚拟机网络连接正常

## 🚀 下一步操作

文件传输完成后，在虚拟机中执行：

```bash
# 进入项目目录
cd /root/movie-review-analysis

# 运行环境检查
./check_environment.sh

# 如果检查通过，运行完整部署
./deploy_complete.sh

# 验证部署结果
./verify_deployment.sh
```
