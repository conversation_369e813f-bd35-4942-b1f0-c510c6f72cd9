#!/bin/bash

# 电影评分分析项目编译脚本
# 功能：编译Java源码并打包成JAR文件

echo "=== 电影评分分析项目编译脚本 ==="

# 设置变量
PROJECT_DIR=$(pwd)
SRC_DIR="$PROJECT_DIR/src"
BUILD_DIR="$PROJECT_DIR/build"
JAR_NAME="movie-review-analysis.jar"

echo "项目目录: $PROJECT_DIR"
echo "源码目录: $SRC_DIR"
echo "构建目录: $BUILD_DIR"
echo "JAR文件名: $JAR_NAME"

# 检查Hadoop环境
echo ""
echo "=== 检查Hadoop环境 ==="
if command -v hadoop &> /dev/null; then
    echo "✓ Hadoop命令可用"
    HADOOP_VERSION=$(hadoop version | head -n 1)
    echo "Hadoop版本: $HADOOP_VERSION"
else
    echo "✗ 错误：找不到Hadoop命令，请检查Hadoop安装和环境变量"
    exit 1
fi

# 获取Hadoop类路径
echo ""
echo "=== 获取Hadoop类路径 ==="
HADOOP_CLASSPATH=$(hadoop classpath)
if [ $? -eq 0 ]; then
    echo "✓ 成功获取Hadoop类路径"
    echo "类路径长度: ${#HADOOP_CLASSPATH} 字符"
else
    echo "✗ 错误：无法获取Hadoop类路径"
    exit 1
fi

# 清理构建目录
echo ""
echo "=== 清理构建目录 ==="
if [ -d "$BUILD_DIR" ]; then
    rm -rf "$BUILD_DIR"/*
    echo "✓ 清理完成"
else
    mkdir -p "$BUILD_DIR"
    echo "✓ 创建构建目录"
fi

# 编译Java源码
echo ""
echo "=== 编译Java源码 ==="
echo "编译命令: javac -cp \$HADOOP_CLASSPATH -d $BUILD_DIR $SRC_DIR/com/moviereview/*.java"

javac -cp "$HADOOP_CLASSPATH" -d "$BUILD_DIR" "$SRC_DIR/com/moviereview/*.java"

# 检查编译结果
if [ $? -eq 0 ]; then
    echo "✓ 编译成功！"
    
    # 显示编译生成的class文件
    echo ""
    echo "生成的class文件："
    find "$BUILD_DIR" -name "*.class" -exec basename {} \;
    
else
    echo "✗ 编译失败！"
    echo "请检查以下几点："
    echo "1. Java源码语法是否正确"
    echo "2. Hadoop类路径是否正确"
    echo "3. Java版本是否兼容"
    exit 1
fi

# 创建JAR包
echo ""
echo "=== 创建JAR包 ==="
cd "$BUILD_DIR"

jar cf "$JAR_NAME" com/moviereview/*.class

if [ $? -eq 0 ]; then
    echo "✓ JAR包创建成功: $BUILD_DIR/$JAR_NAME"
    
    # 显示JAR包信息
    echo ""
    echo "JAR包信息："
    echo "文件大小: $(ls -lh $JAR_NAME | awk '{print $5}')"
    echo "文件路径: $BUILD_DIR/$JAR_NAME"
    
    echo ""
    echo "JAR包内容："
    jar tf "$JAR_NAME"
    
else
    echo "✗ JAR包创建失败！"
    exit 1
fi

# 返回项目根目录
cd "$PROJECT_DIR"

echo ""
echo "=== 编译完成 ==="
echo "✓ 项目编译成功！"
echo "✓ JAR包位置: $BUILD_DIR/$JAR_NAME"
echo ""
echo "下一步操作："
echo "1. 准备输入数据并上传到HDFS"
echo "2. 运行MapReduce作业："
echo "   hadoop jar $BUILD_DIR/$JAR_NAME com.moviereview.MovieReviewAnalysis <输入路径> <输出路径>"
echo ""
echo "示例命令："
echo "   hadoop jar $BUILD_DIR/$JAR_NAME com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews"
