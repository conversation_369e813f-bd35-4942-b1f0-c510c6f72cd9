package com.moviereview;

import java.io.IOException;
import java.text.DecimalFormat;
import org.apache.hadoop.io.DoubleWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

/**
 * 电影评分Reducer类
 * 
 * 功能：
 * 1. 接收来自所有Mapper/Combiner的数据
 * 2. 计算每部电影的最终平均评分
 * 3. 输出最终结果
 * 4. 提供统计信息
 * 
 * 输入格式：(movieId, rating)
 * 输出格式：(movieId, average_rating)
 */
public class MovieRatingReducer extends Reducer<Text, DoubleWritable, Text, DoubleWritable> {
    
    // 重用对象
    private DoubleWritable result = new DoubleWritable();
    
    // 格式化输出，保留两位小数
    private DecimalFormat df = new DecimalFormat("#.##");
    
    // 统计信息
    private long totalMovies = 0;
    private double totalRatings = 0.0;
    private double maxRating = Double.MIN_VALUE;
    private double minRating = Double.MAX_VALUE;
    private String maxRatingMovie = "";
    private String minRatingMovie = "";
    
    @Override
    protected void reduce(Text key, Iterable<DoubleWritable> values, Context context)
            throws IOException, InterruptedException {
        
        double sum = 0.0;
        int count = 0;
        
        // 聚合所有评分
        for (DoubleWritable value : values) {
            sum += value.get();
            count++;
        }
        
        // 计算平均评分
        if (count > 0) {
            double average = sum / count;
            
            // 保留两位小数
            double roundedAverage = Math.round(average * 100.0) / 100.0;
            
            // 输出结果
            result.set(roundedAverage);
            context.write(key, result);
            
            // 更新统计信息
            totalMovies++;
            totalRatings += roundedAverage;
            
            // 记录最高和最低评分
            if (roundedAverage > maxRating) {
                maxRating = roundedAverage;
                maxRatingMovie = key.toString();
            }
            if (roundedAverage < minRating) {
                minRating = roundedAverage;
                minRatingMovie = key.toString();
            }
            
            // 更新计数器
            context.getCounter("Reducer", "MoviesProcessed").increment(1);
            context.getCounter("Reducer", "RatingsProcessed").increment(count);
            
            // 按评分范围统计
            if (roundedAverage >= 4.0) {
                context.getCounter("RatingDistribution", "HighRating_4.0+").increment(1);
            } else if (roundedAverage >= 3.0) {
                context.getCounter("RatingDistribution", "MediumRating_3.0-3.9").increment(1);
            } else if (roundedAverage >= 2.0) {
                context.getCounter("RatingDistribution", "LowRating_2.0-2.9").increment(1);
            } else {
                context.getCounter("RatingDistribution", "VeryLowRating_1.0-1.9").increment(1);
            }
        }
    }
    
    @Override
    protected void cleanup(Context context) throws IOException, InterruptedException {
        // 计算总体平均评分
        double overallAverage = totalMovies > 0 ? totalRatings / totalMovies : 0.0;
        
        // 输出统计信息
        System.out.println("=== Reducer统计信息 ===");
        System.out.println("处理电影总数: " + totalMovies);
        System.out.println("总体平均评分: " + df.format(overallAverage));
        System.out.println("最高评分: " + df.format(maxRating) + " (电影ID: " + maxRatingMovie + ")");
        System.out.println("最低评分: " + df.format(minRating) + " (电影ID: " + minRatingMovie + ")");
        System.out.println("=====================");
        
        // 更新全局计数器
        context.getCounter("FinalStats", "TotalMovies").increment(totalMovies);
        context.getCounter("FinalStats", "OverallAverageRating").increment((long)(overallAverage * 100)); // 乘以100保存为整数
        context.getCounter("FinalStats", "MaxRating").increment((long)(maxRating * 100));
        context.getCounter("FinalStats", "MinRating").increment((long)(minRating * 100));
    }
}
