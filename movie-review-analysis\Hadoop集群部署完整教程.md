# 电影评分分析项目 - Hadoop集群部署完整教程

## 📋 教程概述

本教程提供了从零开始在Hadoop集群上部署和运行电影评分分析MapReduce项目的完整步骤，包含每一步的详细路径和命令。

## 🎯 前置条件检查

### 系统要求
- **操作系统**：Windows 10/11 或 Linux
- **Java版本**：JDK 8 或更高版本
- **内存**：至少4GB RAM
- **磁盘空间**：至少10GB可用空间
- **网络**：稳定的网络连接

### 必需软件
- Java JDK 8+
- Hadoop 3.x
- SSH客户端（Windows需要）

## 第一阶段：Hadoop环境配置

### 步骤1：Java环境验证

**当前路径**：`任意目录`

```cmd
# 检查Java版本
java -version

# 检查Java编译器
javac -version

# 查看Java安装路径
echo %JAVA_HOME%
```

**预期输出**：
```
java version "1.8.0_XXX"
Java(TM) SE Runtime Environment (build 1.8.0_XXX)
Java HotSpot(TM) 64-Bit Server VM (build XXX)
```

**如果Java未安装**：
1. 下载JDK 8：https://www.oracle.com/java/technologies/javase/javase8-archive-downloads.html
2. 安装后设置环境变量：
   ```cmd
   # Windows系统变量设置
   JAVA_HOME=C:\Program Files\Java\jdk1.8.0_XXX
   PATH=%PATH%;%JAVA_HOME%\bin
   ```

### 步骤2：Hadoop安装和配置

**当前路径**：`C:\` (Windows) 或 `/opt/` (Linux)

#### 2.1 下载Hadoop

```cmd
# 创建Hadoop目录
mkdir C:\hadoop
cd C:\hadoop

# 下载Hadoop 3.3.4 (约350MB)
# 访问：https://hadoop.apache.org/releases.html
# 下载：hadoop-3.3.4.tar.gz
```

#### 2.2 解压和配置

**当前路径**：`C:\hadoop`

```cmd
# 解压Hadoop（假设下载到Downloads目录）
tar -xzf %USERPROFILE%\Downloads\hadoop-3.3.4.tar.gz

# 重命名目录
ren hadoop-3.3.4 hadoop-3.3.4

# 设置Hadoop目录
set HADOOP_HOME=C:\hadoop\hadoop-3.3.4
```

#### 2.3 配置环境变量

**系统环境变量设置**：

```cmd
# 添加以下环境变量到系统
HADOOP_HOME=C:\hadoop\hadoop-3.3.4
HADOOP_CONF_DIR=%HADOOP_HOME%\etc\hadoop
HADOOP_MAPRED_HOME=%HADOOP_HOME%
HADOOP_COMMON_HOME=%HADOOP_HOME%
HADOOP_HDFS_HOME=%HADOOP_HOME%
YARN_HOME=%HADOOP_HOME%
PATH=%PATH%;%HADOOP_HOME%\bin;%HADOOP_HOME%\sbin
```

#### 2.4 配置Hadoop核心文件

**当前路径**：`%HADOOP_HOME%\etc\hadoop`

##### 2.4.1 编辑 core-site.xml

**文件路径**：`C:\hadoop\hadoop-3.3.4\etc\hadoop\core-site.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>fs.defaultFS</name>
        <value>hdfs://localhost:9000</value>
        <description>NameNode URI</description>
    </property>
    <property>
        <name>hadoop.tmp.dir</name>
        <value>C:/hadoop/data/tmp</value>
        <description>Temporary directory</description>
    </property>
</configuration>
```

##### 2.4.2 编辑 hdfs-site.xml

**文件路径**：`C:\hadoop\hadoop-3.3.4\etc\hadoop\hdfs-site.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>dfs.replication</name>
        <value>1</value>
        <description>Default block replication</description>
    </property>
    <property>
        <name>dfs.namenode.name.dir</name>
        <value>C:/hadoop/data/namenode</value>
        <description>NameNode directory</description>
    </property>
    <property>
        <name>dfs.datanode.data.dir</name>
        <value>C:/hadoop/data/datanode</value>
        <description>DataNode directory</description>
    </property>
    <property>
        <name>dfs.namenode.http-address</name>
        <value>localhost:9870</value>
        <description>NameNode web interface</description>
    </property>
</configuration>
```

##### 2.4.3 编辑 mapred-site.xml

**文件路径**：`C:\hadoop\hadoop-3.3.4\etc\hadoop\mapred-site.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>mapreduce.framework.name</name>
        <value>yarn</value>
        <description>MapReduce framework</description>
    </property>
    <property>
        <name>mapreduce.application.classpath</name>
        <value>%HADOOP_HOME%/share/hadoop/mapreduce/*,%HADOOP_HOME%/share/hadoop/mapreduce/lib/*</value>
    </property>
</configuration>
```

##### 2.4.4 编辑 yarn-site.xml

**文件路径**：`C:\hadoop\hadoop-3.3.4\etc\hadoop\yarn-site.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
        <description>Auxiliary services</description>
    </property>
    <property>
        <name>yarn.nodemanager.aux-services.mapreduce.shuffle.class</name>
        <value>org.apache.hadoop.mapred.ShuffleHandler</value>
    </property>
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>localhost</value>
    </property>
    <property>
        <name>yarn.resourcemanager.webapp.address</name>
        <value>localhost:8088</value>
    </property>
</configuration>
```

##### 2.4.5 编辑 hadoop-env.cmd (Windows)

**文件路径**：`C:\hadoop\hadoop-3.3.4\etc\hadoop\hadoop-env.cmd`

```cmd
@echo off
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_XXX
set HADOOP_HOME=C:\hadoop\hadoop-3.3.4
set HADOOP_CONF_DIR=%HADOOP_HOME%\etc\hadoop
set HADOOP_LOG_DIR=%HADOOP_HOME%\logs
```

### 步骤3：创建Hadoop数据目录

**当前路径**：`C:\hadoop`

```cmd
# 创建必要的目录
mkdir C:\hadoop\data
mkdir C:\hadoop\data\tmp
mkdir C:\hadoop\data\namenode
mkdir C:\hadoop\data\datanode
mkdir C:\hadoop\logs

# 验证目录结构
dir C:\hadoop\data
```

**预期输出**：
```
C:\hadoop\data 的目录
2025/06/05  datanode
2025/06/05  namenode
2025/06/05  tmp
```

## 第二阶段：Hadoop集群启动

### 步骤4：格式化NameNode

**当前路径**：`%HADOOP_HOME%\bin`

```cmd
# 切换到Hadoop bin目录
cd C:\hadoop\hadoop-3.3.4\bin

# 格式化NameNode（仅第一次需要）
hdfs namenode -format

# 等待格式化完成，看到以下信息表示成功：
# "Storage directory C:\hadoop\data\namenode has been successfully formatted."
```

**重要提示**：格式化会清空所有HDFS数据，只在第一次安装时执行！

### 步骤5：启动Hadoop服务

**当前路径**：`%HADOOP_HOME%\sbin`

```cmd
# 切换到sbin目录
cd C:\hadoop\hadoop-3.3.4\sbin

# 启动HDFS服务
start-dfs.cmd

# 等待启动完成（约30-60秒）

# 启动YARN服务
start-yarn.cmd

# 等待启动完成（约30-60秒）
```

### 步骤6：验证Hadoop服务状态

**当前路径**：`%HADOOP_HOME%\bin`

```cmd
# 切换到bin目录
cd C:\hadoop\hadoop-3.3.4\bin

# 检查Java进程
jps

# 预期看到以下进程：
# XXXX NameNode
# XXXX DataNode
# XXXX ResourceManager
# XXXX NodeManager
# XXXX SecondaryNameNode
```

**Web界面验证**：
- **NameNode Web UI**：http://localhost:9870
- **ResourceManager Web UI**：http://localhost:8088
- **DataNode信息**：http://localhost:9864

### 步骤7：测试HDFS基本操作

**当前路径**：`%HADOOP_HOME%\bin`

```cmd
# 创建用户目录
hdfs dfs -mkdir -p /user/%USERNAME%

# 查看HDFS根目录
hdfs dfs -ls /

# 查看用户目录
hdfs dfs -ls /user

# 检查HDFS状态
hdfs dfsadmin -report
```

**预期输出**：
```
Configured Capacity: XXXXX (XX GB)
Present Capacity: XXXXX (XX GB)
DFS Remaining: XXXXX (XX GB)
DFS Used: XXXXX (XX MB)
Live datanodes (1):
Name: 127.0.0.1:9866 (localhost)
```

## 第三阶段：项目部署和数据准备

### 步骤8：准备项目环境

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 确认当前在项目目录
cd D:\ldlhadoopxhd\movie-review-analysis

# 查看项目结构
dir

# 预期看到：
# build/
# src/
# compile.bat
# prepare_data.bat
# run_job.bat
# README.md
```

### 步骤9：编译MapReduce项目

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 设置Hadoop类路径
set HADOOP_CLASSPATH=%HADOOP_HOME%\share\hadoop\common\*;%HADOOP_HOME%\share\hadoop\common\lib\*;%HADOOP_HOME%\share\hadoop\hdfs\*;%HADOOP_HOME%\share\hadoop\hdfs\lib\*;%HADOOP_HOME%\share\hadoop\mapreduce\*;%HADOOP_HOME%\share\hadoop\mapreduce\lib\*;%HADOOP_HOME%\share\hadoop\yarn\*;%HADOOP_HOME%\share\hadoop\yarn\lib\*

# 清理构建目录
rmdir /s /q build
mkdir build

# 编译Java源码
javac -cp %HADOOP_CLASSPATH% -d build src\com\moviereview\*.java

# 创建JAR包
cd build
jar cf movie-review-analysis.jar com\moviereview\*.class

# 验证JAR包
jar tf movie-review-analysis.jar

# 返回项目根目录
cd ..
```

**预期输出**：
```
com/moviereview/MovieReviewAnalysis.class
com/moviereview/MovieRatingMapper.class
com/moviereview/MovieRatingCombiner.class
com/moviereview/MovieRatingPartitioner.class
com/moviereview/MovieRatingReducer.class
```

### 步骤10：准备输入数据

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 创建本地数据目录
mkdir data

# 处理原始数据（去除标题行）
powershell -Command "Get-Content '..\ratings.csv' | Select-Object -Skip 1 | Out-File -FilePath 'data\clean_ratings.csv' -Encoding UTF8"

# 查看处理后的数据
powershell -Command "Get-Content 'data\clean_ratings.csv' | Select-Object -First 5"

# 检查数据文件大小
dir data\clean_ratings.csv
```

### 步骤11：上传数据到HDFS

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 创建HDFS输入目录
hdfs dfs -mkdir -p /user/input/moviereviews

# 上传数据文件到HDFS
hdfs dfs -put data\clean_ratings.csv /user/input/moviereviews/

# 验证上传成功
hdfs dfs -ls /user/input/moviereviews

# 查看HDFS中的数据样本
hdfs dfs -cat /user/input/moviereviews/clean_ratings.csv | more
```

**预期输出**：
```
Found 1 items
-rw-r--r--   1 <USER> <GROUP>  XXXXXXX 2025-06-05 XX:XX /user/input/moviereviews/clean_ratings.csv
```

## 第四阶段：MapReduce作业执行

### 步骤12：提交MapReduce作业

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 确保输出目录不存在（如果存在则删除）
hdfs dfs -rm -r /user/output/moviereviews

# 提交MapReduce作业
hadoop jar build\movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews

# 作业提交后会显示进度信息：
# INFO mapreduce.Job: Running job: job_XXXXXXXXX_XXXX
# INFO mapreduce.Job: map 0% reduce 0%
# INFO mapreduce.Job: map 50% reduce 0%
# INFO mapreduce.Job: map 100% reduce 0%
# INFO mapreduce.Job: map 100% reduce 100%
# INFO mapreduce.Job: Job job_XXXXXXXXX_XXXX completed successfully
```

**作业监控**：
- **实时监控**：访问 http://localhost:8088
- **作业历史**：http://localhost:19888 (如果启用了历史服务器)

### 步骤13：查看作业执行结果

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 查看输出目录结构
hdfs dfs -ls /user/output/moviereviews

# 预期看到：
# _SUCCESS
# part-r-00000
# part-r-00001 (如果有多个Reducer)

# 查看结果内容
hdfs dfs -cat /user/output/moviereviews/part-r-*

# 查看前20行结果
hdfs dfs -cat /user/output/moviereviews/part-r-* | more

# 统计结果行数
hdfs dfs -cat /user/output/moviereviews/part-r-* | wc -l
```

### 步骤14：下载和分析结果

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 下载结果到本地
hdfs dfs -getmerge /user/output/moviereviews/part-r-* movie_analysis_result.txt

# 查看本地结果文件
type movie_analysis_result.txt | more

# 分析结果 - 找出评分最高的10部电影
powershell -Command "Get-Content 'movie_analysis_result.txt' | ForEach-Object { $parts = $_ -split '\t'; [PSCustomObject]@{MovieID=$parts[0]; Rating=[double]$parts[1]} } | Sort-Object Rating -Descending | Select-Object -First 10"

# 分析结果 - 找出评分最低的10部电影
powershell -Command "Get-Content 'movie_analysis_result.txt' | ForEach-Object { $parts = $_ -split '\t'; [PSCustomObject]@{MovieID=$parts[0]; Rating=[double]$parts[1]} } | Sort-Object Rating | Select-Object -First 10"

# 统计评分分布
powershell -Command "$data = Get-Content 'movie_analysis_result.txt' | ForEach-Object { $parts = $_ -split '\t'; [double]$parts[1] }; $high = ($data | Where-Object { $_ -ge 4.0 }).Count; $medium = ($data | Where-Object { $_ -ge 3.0 -and $_ -lt 4.0 }).Count; $low = ($data | Where-Object { $_ -lt 3.0 }).Count; Write-Host '高评分(4.0+):' $high '部'; Write-Host '中评分(3.0-3.9):' $medium '部'; Write-Host '低评分(<3.0):' $low '部'"
```

## 第五阶段：性能分析和优化

### 步骤15：查看作业性能统计

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 查看最近的作业ID
yarn application -list -appStates FINISHED | findstr "movie"

# 查看作业详细日志（替换APPLICATION_ID）
yarn logs -applicationId application_XXXXXXXXX_XXXX

# 查看作业统计信息
hadoop job -history all

# 查看HDFS使用情况
hdfs dfsadmin -report
```

### 步骤16：验证优化效果

**Web界面分析**：

1. **访问ResourceManager** (http://localhost:8088)
   - 查看作业执行时间
   - 分析Map/Reduce任务分布
   - 检查资源使用情况

2. **访问NameNode** (http://localhost:9870)
   - 查看HDFS存储使用
   - 检查数据块分布
   - 验证数据复制情况

**性能指标分析**：
- **Combiner效果**：查看Map输出记录数 vs Reduce输入记录数
- **数据本地性**：检查本地Map任务比例
- **资源利用率**：分析CPU和内存使用情况

## 第六阶段：清理和维护

### 步骤17：清理测试数据（可选）

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 删除HDFS中的输入数据
hdfs dfs -rm -r /user/input/moviereviews

# 删除HDFS中的输出数据
hdfs dfs -rm -r /user/output/moviereviews

# 清理本地临时文件
del movie_analysis_result.txt
rmdir /s /q data
```

### 步骤18：停止Hadoop服务

**当前路径**：`%HADOOP_HOME%\sbin`

```cmd
# 切换到sbin目录
cd C:\hadoop\hadoop-3.3.4\sbin

# 停止YARN服务
stop-yarn.cmd

# 停止HDFS服务
stop-dfs.cmd

# 验证服务已停止
jps
```

## 🎯 完整执行脚本

### 一键部署脚本 - deploy_complete.bat

**文件路径**：`D:\ldlhadoopxhd\movie-review-analysis\deploy_complete.bat`

```cmd
@echo off
echo ========================================
echo   电影评分分析 - Hadoop集群完整部署
echo ========================================

set PROJECT_DIR=D:\ldlhadoopxhd\movie-review-analysis
set HADOOP_HOME=C:\hadoop\hadoop-3.3.4

echo [1/8] 检查环境...
cd %PROJECT_DIR%

echo 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo X Java环境未配置，请先安装Java JDK 8+
    pause
    exit /b 1
)

echo 检查Hadoop环境...
if not exist "%HADOOP_HOME%" (
    echo X Hadoop未安装，请先安装Hadoop到 %HADOOP_HOME%
    pause
    exit /b 1
)

echo [2/8] 启动Hadoop服务...
cd %HADOOP_HOME%\sbin
call start-dfs.cmd
timeout /t 30 /nobreak
call start-yarn.cmd
timeout /t 30 /nobreak

echo [3/8] 验证Hadoop服务...
cd %HADOOP_HOME%\bin
jps | findstr "NameNode DataNode ResourceManager NodeManager"
if %errorlevel% neq 0 (
    echo X Hadoop服务启动失败
    pause
    exit /b 1
)

echo [4/8] 编译MapReduce项目...
cd %PROJECT_DIR%
set HADOOP_CLASSPATH=%HADOOP_HOME%\share\hadoop\common\*;%HADOOP_HOME%\share\hadoop\common\lib\*;%HADOOP_HOME%\share\hadoop\hdfs\*;%HADOOP_HOME%\share\hadoop\hdfs\lib\*;%HADOOP_HOME%\share\hadoop\mapreduce\*;%HADOOP_HOME%\share\hadoop\mapreduce\lib\*;%HADOOP_HOME%\share\hadoop\yarn\*;%HADOOP_HOME%\share\hadoop\yarn\lib\*

if exist build rmdir /s /q build
mkdir build

javac -cp "%HADOOP_CLASSPATH%" -d build src\com\moviereview\*.java
if %errorlevel% neq 0 (
    echo X 编译失败
    pause
    exit /b 1
)

cd build
jar cf movie-review-analysis.jar com\moviereview\*.class
cd ..

echo [5/8] 准备数据...
if not exist data mkdir data
powershell -Command "Get-Content '..\ratings.csv' | Select-Object -Skip 1 | Out-File -FilePath 'data\clean_ratings.csv' -Encoding UTF8"

echo [6/8] 上传数据到HDFS...
hdfs dfs -mkdir -p /user/input/moviereviews
hdfs dfs -rm -f /user/input/moviereviews/clean_ratings.csv
hdfs dfs -put data\clean_ratings.csv /user/input/moviereviews/

echo [7/8] 运行MapReduce作业...
hdfs dfs -rm -r /user/output/moviereviews
hadoop jar build\movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews

echo [8/8] 下载和分析结果...
hdfs dfs -getmerge /user/output/moviereviews/part-r-* movie_analysis_result.txt

echo ========================================
echo 部署完成！结果文件：movie_analysis_result.txt
echo Web界面：
echo   NameNode: http://localhost:9870
echo   ResourceManager: http://localhost:8088
echo ========================================
pause
```

## 📋 故障排除指南

### 常见问题1：Java环境问题

**症状**：`'java' 不是内部或外部命令`

**解决方案**：
```cmd
# 当前路径：任意目录
# 1. 检查Java安装
where java

# 2. 如果未找到，设置JAVA_HOME
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_XXX
set PATH=%PATH%;%JAVA_HOME%\bin

# 3. 验证设置
java -version
```

### 常见问题2：Hadoop服务启动失败

**症状**：`jps`命令看不到NameNode或DataNode

**解决方案**：
```cmd
# 当前路径：%HADOOP_HOME%\logs
cd C:\hadoop\hadoop-3.3.4\logs

# 1. 查看错误日志
type hadoop-*-namenode-*.log | findstr "ERROR"
type hadoop-*-datanode-*.log | findstr "ERROR"

# 2. 常见解决方法
# 重新格式化NameNode（会丢失数据！）
cd %HADOOP_HOME%\bin
hdfs namenode -format -force

# 3. 重启服务
cd %HADOOP_HOME%\sbin
call stop-dfs.cmd
call stop-yarn.cmd
timeout /t 10
call start-dfs.cmd
call start-yarn.cmd
```

### 常见问题3：HDFS权限问题

**症状**：`Permission denied: user=XXX, access=WRITE`

**解决方案**：
```cmd
# 当前路径：%HADOOP_HOME%\bin
cd C:\hadoop\hadoop-3.3.4\bin

# 1. 创建用户目录
hdfs dfs -mkdir -p /user/%USERNAME%

# 2. 设置权限
hdfs dfs -chmod 755 /user/%USERNAME%

# 3. 验证权限
hdfs dfs -ls /user
```

### 常见问题4：编译错误

**症状**：`找不到符号` 或 `程序包不存在`

**解决方案**：
```cmd
# 当前路径：D:\ldlhadoopxhd\movie-review-analysis

# 1. 检查Hadoop类路径
echo %HADOOP_CLASSPATH%

# 2. 重新设置类路径
set HADOOP_CLASSPATH=%HADOOP_HOME%\share\hadoop\common\*;%HADOOP_HOME%\share\hadoop\common\lib\*;%HADOOP_HOME%\share\hadoop\hdfs\*;%HADOOP_HOME%\share\hadoop\hdfs\lib\*;%HADOOP_HOME%\share\hadoop\mapreduce\*;%HADOOP_HOME%\share\hadoop\mapreduce\lib\*;%HADOOP_HOME%\share\hadoop\yarn\*;%HADOOP_HOME%\share\hadoop\yarn\lib\*

# 3. 重新编译
javac -cp "%HADOOP_CLASSPATH%" -d build src\com\moviereview\*.java
```

### 常见问题5：作业提交失败

**症状**：`Application application_XXX failed`

**解决方案**：
```cmd
# 当前路径：D:\ldlhadoopxhd\movie-review-analysis

# 1. 检查输入数据
hdfs dfs -ls /user/input/moviereviews

# 2. 检查JAR包
jar tf build\movie-review-analysis.jar

# 3. 查看详细错误
yarn logs -applicationId application_XXXXXXXXX_XXXX

# 4. 检查资源管理器状态
# 访问：http://localhost:8088
```

## 📊 性能优化建议

### 优化1：调整Map/Reduce任务数

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
# 提交作业时指定参数
hadoop jar build\movie-review-analysis.jar com.moviereview.MovieReviewAnalysis \
  -Dmapreduce.job.maps=4 \
  -Dmapreduce.job.reduces=2 \
  -Dmapreduce.map.memory.mb=1024 \
  -Dmapreduce.reduce.memory.mb=2048 \
  /user/input/moviereviews /user/output/moviereviews
```

### 优化2：启用压缩

```cmd
# 启用中间结果压缩
hadoop jar build\movie-review-analysis.jar com.moviereview.MovieReviewAnalysis \
  -Dmapreduce.map.output.compress=true \
  -Dmapreduce.map.output.compress.codec=org.apache.hadoop.io.compress.SnappyCodec \
  /user/input/moviereviews /user/output/moviereviews
```

### 优化3：调整块大小

```cmd
# 设置HDFS块大小（适合大文件）
hdfs dfs -D dfs.blocksize=268435456 -put data\clean_ratings.csv /user/input/moviereviews/
```

## 🎯 验收标准

### 成功标准检查清单

**环境验证**：
- [ ] Java版本 >= 1.8
- [ ] Hadoop服务全部启动（5个进程）
- [ ] Web界面可访问（9870, 8088）
- [ ] HDFS基本操作正常

**项目验证**：
- [ ] Java代码编译成功
- [ ] JAR包包含所有类文件
- [ ] 数据成功上传到HDFS
- [ ] MapReduce作业成功完成

**结果验证**：
- [ ] 输出目录包含结果文件
- [ ] 结果格式正确（movieId\taverage_rating）
- [ ] 结果数量合理（>0条记录）
- [ ] 平均评分在1.0-5.0范围内

**性能验证**：
- [ ] Combiner减少了网络传输
- [ ] 作业完成时间合理
- [ ] 资源使用正常
- [ ] 无错误或警告信息

## 📝 最终检查命令

**当前路径**：`D:\ldlhadoopxhd\movie-review-analysis`

```cmd
echo ========================================
echo 最终验收检查
echo ========================================

echo [1] Hadoop服务状态：
jps

echo [2] HDFS状态：
hdfs dfsadmin -report | findstr "Live datanodes"

echo [3] 输入数据：
hdfs dfs -ls /user/input/moviereviews

echo [4] 输出结果：
hdfs dfs -ls /user/output/moviereviews

echo [5] 结果样本：
hdfs dfs -cat /user/output/moviereviews/part-r-* | head -10

echo [6] 结果统计：
hdfs dfs -cat /user/output/moviereviews/part-r-* | wc -l

echo ========================================
echo 检查完成
echo ========================================
```

## 🎉 部署完成

恭喜！您已经成功完成了电影评分分析项目的Hadoop集群部署。

**项目成果**：
- ✅ 完整的Hadoop集群环境
- ✅ 成功运行的MapReduce作业
- ✅ 大数据处理结果分析
- ✅ 性能优化验证

**下一步建议**：
1. 尝试处理更大规模的数据集
2. 实现更复杂的分析功能
3. 学习Spark等更高级的大数据技术
4. 部署到真实的分布式集群环境

**技术收获**：
- 掌握了Hadoop集群的完整部署流程
- 理解了MapReduce的工作原理和优化策略
- 学会了大数据项目的开发和调试方法
- 具备了分布式计算的实践经验
