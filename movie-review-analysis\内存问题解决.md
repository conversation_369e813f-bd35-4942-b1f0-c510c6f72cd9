# 虚拟内存限制问题解决方案

## 🚨 问题分析

错误信息显示：
- 容器使用了 2.6 GB 虚拟内存，超过了 2.1 GB 的限制
- 物理内存使用正常（约100MB），主要是虚拟内存超限
- 需要调整YARN的内存配置

## 🔧 解决方案

### 方案1：禁用虚拟内存检查（推荐）

```bash
# 停止YARN服务
stop-yarn.sh

# 编辑yarn-site.xml配置文件
vim /opt/hadoop/current/etc/hadoop/yarn-site.xml

# 在<configuration>标签内添加以下配置：
```

在yarn-site.xml中添加：
```xml
<property>
    <name>yarn.nodemanager.vmem-check-enabled</name>
    <value>false</value>
    <description>禁用虚拟内存检查</description>
</property>
```

完整的yarn-site.xml应该是：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
    </property>
    <property>
        <name>yarn.nodemanager.aux-services.mapreduce.shuffle.class</name>
        <value>org.apache.hadoop.mapred.ShuffleHandler</value>
    </property>
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>localhost</value>
    </property>
    <property>
        <name>yarn.resourcemanager.webapp.address</name>
        <value>0.0.0.0:8088</value>
    </property>
    <property>
        <name>yarn.nodemanager.vmem-check-enabled</name>
        <value>false</value>
        <description>禁用虚拟内存检查</description>
    </property>
</configuration>
```

```bash
# 重新启动YARN服务
start-yarn.sh

# 验证服务状态
jps
```

### 方案2：调整虚拟内存比例

如果不想禁用检查，可以调整虚拟内存比例：

```xml
<property>
    <name>yarn.nodemanager.vmem-pmem-ratio</name>
    <value>4</value>
    <description>虚拟内存与物理内存的比例，默认2.1，调整为4</description>
</property>
```

### 方案3：减少Map任务内存

```xml
<property>
    <name>yarn.app.mapreduce.am.resource.mb</name>
    <value>512</value>
    <description>ApplicationMaster内存</description>
</property>
<property>
    <name>mapreduce.map.memory.mb</name>
    <value>512</value>
    <description>Map任务内存</description>
</property>
<property>
    <name>mapreduce.reduce.memory.mb</name>
    <value>1024</value>
    <description>Reduce任务内存</description>
</property>
```

## 🚀 快速修复步骤

### 第一步：修改配置文件

```bash
# 停止YARN
stop-yarn.sh

# 备份原配置
cp /opt/hadoop/current/etc/hadoop/yarn-site.xml /opt/hadoop/current/etc/hadoop/yarn-site.xml.bak

# 重新创建yarn-site.xml
cat > /opt/hadoop/current/etc/hadoop/yarn-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
    </property>
    <property>
        <name>yarn.nodemanager.aux-services.mapreduce.shuffle.class</name>
        <value>org.apache.hadoop.mapred.ShuffleHandler</value>
    </property>
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>localhost</value>
    </property>
    <property>
        <name>yarn.resourcemanager.webapp.address</name>
        <value>0.0.0.0:8088</value>
    </property>
    <property>
        <name>yarn.nodemanager.vmem-check-enabled</name>
        <value>false</value>
        <description>禁用虚拟内存检查</description>
    </property>
    <property>
        <name>yarn.nodemanager.resource.memory-mb</name>
        <value>2048</value>
        <description>NodeManager可用内存</description>
    </property>
    <property>
        <name>yarn.app.mapreduce.am.resource.mb</name>
        <value>512</value>
        <description>ApplicationMaster内存</description>
    </property>
    <property>
        <name>mapreduce.map.memory.mb</name>
        <value>512</value>
        <description>Map任务内存</description>
    </property>
    <property>
        <name>mapreduce.reduce.memory.mb</name>
        <value>1024</value>
        <description>Reduce任务内存</description>
    </property>
</configuration>
EOF
```

### 第二步：重启服务

```bash
# 启动YARN
start-yarn.sh

# 验证服务状态
jps
# 应该看到ResourceManager和NodeManager

# 检查YARN状态
yarn node -list
```

### 第三步：重新运行作业

```bash
# 进入项目目录
cd /root/movie-review-analysis

# 清理之前的输出
hdfs dfs -rm -r /user/output/moviereviews

# 重新提交作业
hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews
```

## 🔍 验证修复

### 检查配置是否生效

```bash
# 查看YARN配置
yarn conf | grep vmem

# 查看NodeManager日志
tail -f /opt/hadoop/current/logs/yarn-root-nodemanager-*.log
```

### 监控作业执行

```bash
# 在另一个终端监控作业
watch -n 2 "yarn application -list"

# 查看Web界面
# http://虚拟机IP:8088
```

## 📊 内存使用优化建议

### 对于虚拟机环境

```xml
<!-- 适合2GB虚拟机的配置 -->
<property>
    <name>yarn.nodemanager.resource.memory-mb</name>
    <value>1536</value>
</property>
<property>
    <name>yarn.app.mapreduce.am.resource.mb</name>
    <value>512</value>
</property>
<property>
    <name>mapreduce.map.memory.mb</name>
    <value>512</value>
</property>
<property>
    <name>mapreduce.reduce.memory.mb</name>
    <value>512</value>
</property>
<property>
    <name>mapreduce.map.java.opts</name>
    <value>-Xmx410m</value>
</property>
<property>
    <name>mapreduce.reduce.java.opts</name>
    <value>-Xmx410m</value>
</property>
```

## ⚠️ 注意事项

1. **禁用虚拟内存检查**是最简单的解决方案，适合开发和测试环境
2. **生产环境**建议调整内存配置而不是禁用检查
3. **虚拟机内存**建议至少分配2GB给CentOS虚拟机
4. **重启服务**后配置才会生效

## 🎯 预期结果

修复后应该看到：
- 作业正常提交和执行
- Map任务成功完成
- 没有内存相关的错误信息
- 作业最终成功完成

## 📝 完整的修复命令

```bash
# 一键修复脚本
#!/bin/bash
echo "修复YARN内存配置..."

# 停止YARN
stop-yarn.sh

# 修改配置
cat > /opt/hadoop/current/etc/hadoop/yarn-site.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
    </property>
    <property>
        <name>yarn.nodemanager.aux-services.mapreduce.shuffle.class</name>
        <value>org.apache.hadoop.mapred.ShuffleHandler</value>
    </property>
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>localhost</value>
    </property>
    <property>
        <name>yarn.resourcemanager.webapp.address</name>
        <value>0.0.0.0:8088</value>
    </property>
    <property>
        <name>yarn.nodemanager.vmem-check-enabled</name>
        <value>false</value>
    </property>
</configuration>
EOF

# 重启YARN
start-yarn.sh

echo "修复完成，可以重新运行作业"
```
