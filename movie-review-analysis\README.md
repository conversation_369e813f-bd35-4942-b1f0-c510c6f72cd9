# 电影网站用户影评分析 - MapReduce项目

## 项目概述

本项目是一个基于Hadoop MapReduce的大数据分析项目，用于分析电影网站的用户评分数据。项目实现了电影平均评分计算，并使用了Combiner和Partitioner优化策略来提高性能。

## 功能特性

- **电影平均评分计算**：计算每部电影的平均评分
- **Combiner优化**：在Map端进行本地聚合，减少网络传输
- **Partitioner优化**：确保数据均匀分布，避免数据倾斜
- **数据统计**：提供详细的处理统计信息
- **错误处理**：完善的异常处理和数据验证

## 项目结构

```
movie-review-analysis/
├── src/                                    # Java源代码目录
│   └── com/
│       └── moviereview/
│           ├── MovieReviewAnalysis.java    # 主驱动类
│           ├── MovieRatingMapper.java      # Mapper类
│           ├── MovieRatingCombiner.java    # Combiner类
│           ├── MovieRatingPartitioner.java # Partitioner类
│           └── MovieRatingReducer.java     # Reducer类
├── build/                                  # 编译输出目录
├── data/                                   # 数据文件目录
├── lib/                                    # 依赖库目录
├── compile.sh                              # Linux编译脚本
├── compile.bat                             # Windows编译脚本
├── prepare_data.bat                        # 数据预处理脚本
├── run_job.bat                             # 作业运行脚本
└── README.md                               # 项目说明文档
```

## 快速开始

### 1. 环境要求

- Java 8 或更高版本
- Hadoop 3.x
- Windows 10/11 或 Linux

### 2. 编译项目

**Windows环境：**
```cmd
compile.bat
```

**Linux环境：**
```bash
chmod +x compile.sh
./compile.sh
```

### 3. 准备数据

```cmd
prepare_data.bat
```

### 4. 运行作业

```cmd
run_job.bat
```

## 详细使用说明

### 数据格式

输入数据格式（CSV）：
```
RATING_ID,USER_MD5,MOVIE_ID,RATING,RATING_TIME
1359352573,0ab7e3efacd56983f16503572d2b9915,5113101,2,2018-09-05 19:42:07
```

输出数据格式：
```
电影ID    平均评分
5113101   1.5
3718526   3.5
```

### 手动执行步骤

1. **编译代码**
```cmd
javac -cp $(hadoop classpath) -d build src/com/moviereview/*.java
cd build
jar cf movie-review-analysis.jar com/moviereview/*.class
```

2. **准备数据**
```cmd
hadoop fs -mkdir -p /user/input/moviereviews
hadoop fs -put data/clean_ratings.csv /user/input/moviereviews/
```

3. **运行作业**
```cmd
hadoop fs -rm -r /user/output/moviereviews
hadoop jar build/movie-review-analysis.jar com.moviereview.MovieReviewAnalysis /user/input/moviereviews /user/output/moviereviews
```

4. **查看结果**
```cmd
hadoop fs -cat /user/output/moviereviews/part-r-*
```

## 优化策略

### Combiner优化

- **原理**：在Map端进行本地聚合
- **效果**：减少网络传输数据量60-80%
- **实现**：MovieRatingCombiner类

### Partitioner优化

- **原理**：控制数据分发策略
- **效果**：确保负载均衡，避免数据倾斜
- **实现**：MovieRatingPartitioner类

## 性能监控

项目提供了详细的性能计数器：

- **数据处理统计**：总行数、有效行数、无效行数
- **Combiner统计**：输入记录数、输出记录数、压缩比
- **评分分布统计**：不同评分范围的电影数量
- **最值统计**：最高评分和最低评分电影

## 常见问题

### 编译问题

**问题**：找不到Hadoop类
**解决**：确保HADOOP_HOME环境变量已设置

### 运行问题

**问题**：作业提交失败
**解决**：检查Hadoop集群状态，确保NameNode和DataNode正常运行

### 数据问题

**问题**：解析错误
**解决**：检查输入数据格式，确保CSV格式正确

## 扩展功能

项目可以扩展以下功能：

1. **评分范围统计**：统计不同评分范围的电影数量
2. **最值查找**：找出评分最高和最低的电影
3. **时间序列分析**：按时间分析评分趋势
4. **用户行为分析**：分析用户评分模式

## 技术栈

- **编程语言**：Java 8
- **大数据框架**：Apache Hadoop 3.x
- **编程模型**：MapReduce
- **构建工具**：原生javac + jar
- **脚本语言**：Batch Script (Windows)

## 学习目标

通过本项目，学生将掌握：

1. MapReduce编程模型
2. Hadoop生态系统使用
3. 大数据处理技术
4. 性能优化策略
5. 分布式计算原理

## 作者

Hadoop期末作业项目

## 许可证

本项目仅用于教育目的。
